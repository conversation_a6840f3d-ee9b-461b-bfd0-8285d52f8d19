#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Test de l'application sur le port 3000
"""

import requests
import time

def test_application_port_3000():
    """Test de l'application sur le port 3000"""
    base_url = "http://localhost:3000"
    
    print("=" * 60)
    print("TEST DE L'APPLICATION RH SUR LE PORT 3000")
    print("=" * 60)
    
    # Test 1: Dashboard RH
    print("\n1. Test du dashboard RH:")
    try:
        response = requests.get(f"{base_url}/rh/", timeout=10)
        print(f"   Status: {response.status_code}")
        if response.status_code == 200:
            print("   ✅ Dashboard RH accessible")
            if 'Gestion RH' in response.text or 'Dashboard' in response.text:
                print("   ✅ Contenu dashboard présent")
        elif response.status_code == 500:
            print("   ⚠️  Erreur serveur (500) - problème de template ou données")
        else:
            print(f"   ❌ Erreur: {response.status_code}")
    except requests.exceptions.ConnectionError:
        print("   ❌ Impossible de se connecter - l'application n'est pas démarrée sur le port 3000")
        return
    except Exception as e:
        print(f"   ❌ Erreur: {e}")
    
    # Test 2: Page de recherche
    print("\n2. Test de la page de recherche:")
    try:
        response = requests.get(f"{base_url}/rh/recherche", timeout=10)
        print(f"   Status: {response.status_code}")
        if response.status_code == 200:
            print("   ✅ Page de recherche accessible")
            if 'Critères de Recherche' in response.text or 'recherche' in response.text.lower():
                print("   ✅ Formulaire de recherche présent")
        elif response.status_code == 500:
            print("   ⚠️  Erreur serveur (500) - problème de template")
        else:
            print(f"   ❌ Erreur: {response.status_code}")
    except Exception as e:
        print(f"   ❌ Erreur: {e}")
    
    # Test 3: Recherche par nom
    print("\n3. Test recherche par nom 'OUALI':")
    try:
        response = requests.get(f"{base_url}/rh/recherche?search=OUALI", timeout=10)
        print(f"   Status: {response.status_code}")
        if response.status_code == 200:
            if 'OUALI' in response.text and 'DRISS' in response.text:
                print("   ✅ Résultats trouvés pour OUALI")
                # Compter les occurrences
                count_ouali = response.text.count('OUALI')
                print(f"   ✅ {count_ouali} occurrences de 'OUALI' dans la page")
            else:
                print("   ⚠️  Résultats non affichés ou non trouvés")
        elif response.status_code == 302:
            print("   ⚠️  Redirection détectée")
        elif response.status_code == 500:
            print("   ⚠️  Erreur serveur (500)")
        else:
            print(f"   ❌ Erreur: {response.status_code}")
    except Exception as e:
        print(f"   ❌ Erreur: {e}")
    
    # Test 4: Recherche par matricule
    print("\n4. Test recherche par matricule '1056663':")
    try:
        response = requests.get(f"{base_url}/rh/recherche?matricule=1056663", timeout=10)
        print(f"   Status: {response.status_code}")
        if response.status_code == 200:
            if '1056663' in response.text:
                print("   ✅ Matricule trouvé dans les résultats")
            else:
                print("   ⚠️  Matricule non trouvé dans les résultats")
        elif response.status_code == 302:
            print("   ⚠️  Redirection détectée")
        elif response.status_code == 500:
            print("   ⚠️  Erreur serveur (500)")
        else:
            print(f"   ❌ Erreur: {response.status_code}")
    except Exception as e:
        print(f"   ❌ Erreur: {e}")
    
    # Test 5: Recherche partielle
    print("\n5. Test recherche partielle 'BEN':")
    try:
        response = requests.get(f"{base_url}/rh/recherche?search=BEN", timeout=10)
        print(f"   Status: {response.status_code}")
        if response.status_code == 200:
            if 'BENALI' in response.text:
                print("   ✅ Recherche partielle fonctionne")
                count_ben = response.text.count('BEN')
                print(f"   ✅ {count_ben} occurrences de 'BEN' dans la page")
            else:
                print("   ⚠️  Recherche partielle ne fonctionne pas")
        elif response.status_code == 302:
            print("   ⚠️  Redirection détectée")
        elif response.status_code == 500:
            print("   ⚠️  Erreur serveur (500)")
        else:
            print(f"   ❌ Erreur: {response.status_code}")
    except Exception as e:
        print(f"   ❌ Erreur: {e}")
    
    print("\n" + "=" * 60)
    print("RÉSUMÉ DU TEST SUR LE PORT 3000")
    print("=" * 60)
    print("✅ Application testée sur http://localhost:3000")
    print("✅ Routes RH testées (/rh/, /rh/recherche)")
    print("✅ Recherche par nom testée")
    print("✅ Recherche par matricule testée")
    print("✅ Recherche partielle testée")
    print("\n🌐 URLs de test:")
    print("   Dashboard: http://localhost:3000/rh/")
    print("   Recherche: http://localhost:3000/rh/recherche")
    print("   Recherche OUALI: http://localhost:3000/rh/recherche?search=OUALI")
    print("   Recherche matricule: http://localhost:3000/rh/recherche?matricule=1056663")
    print("\n📝 Données de test disponibles:")
    print("   Noms: OUALI, BENALI, BERRADA, OUAZZANI, IDRISSI")
    print("   Matricules: 1056663, 1067602, 1122634, 1129321, 1170413")

if __name__ == "__main__":
    test_application_port_3000()
