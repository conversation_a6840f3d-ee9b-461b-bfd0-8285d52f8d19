#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Correction finale du template pour afficher CIN et Unité
"""

import os
import re

def correction_finale():
    """Correction finale du template"""
    
    template_path = "templates/rh/recherche_personnel.html"
    
    try:
        # Lire le contenu
        with open(template_path, 'r', encoding='utf-8') as f:
            contenu = f.read()
        
        print("🔧 Correction finale du template...")
        
        # Correction 1: Remplacer toute la section CIN
        ancien_cin = r'<td>\s*{% if militaire\.cin_date_expiration %}.*?{% endif %}\s*</td>'
        nouveau_cin = '''<td>
                                        <div class="d-flex flex-column">
                                            <small class="fw-bold">{{ militaire.numero_cin if militaire.numero_cin else 'N/A' }}</small>
                                            {% if militaire.date_expiration_cin %}
                                            {% set jours_restants = (militaire.date_expiration_cin - date.today()).days %}
                                            {% if jours_restants <= 0 %}
                                            <span class="badge bg-danger mt-1">
                                                <i class="fas fa-exclamation"></i> Expirée
                                            </span>
                                            {% elif jours_restants <= 30 %}
                                            <span class="badge bg-warning text-dark mt-1">
                                                <i class="fas fa-clock"></i> {{ jours_restants }}j
                                            </span>
                                            {% else %}
                                            <span class="badge bg-success mt-1">
                                                <i class="fas fa-check"></i> Valide
                                            </span>
                                            {% endif %}
                                            {% endif %}
                                        </div>
                                    </td>'''
        
        # Appliquer la correction avec regex
        contenu_corrige = re.sub(ancien_cin, nouveau_cin, contenu, flags=re.DOTALL)
        
        # Correction 2: S'assurer que l'en-tête est correct
        contenu_corrige = contenu_corrige.replace('<th>Service</th>', '<th>Arme</th>')
        
        # Sauvegarder
        with open(template_path, 'w', encoding='utf-8') as f:
            f.write(contenu_corrige)
        
        print("✅ Correction finale appliquée")
        
        return True
        
    except Exception as e:
        print(f"❌ Erreur : {str(e)}")
        return False

def test_template_final():
    """Test final du template"""
    
    template_path = "templates/rh/recherche_personnel.html"
    
    try:
        with open(template_path, 'r', encoding='utf-8') as f:
            contenu = f.read()
        
        print("\n🧪 Test final du template...")
        
        # Vérifications
        checks = [
            ('numero_cin', '✅ Numéro CIN affiché'),
            ('unite.libelle', '✅ Libellé unité affiché'),
            ('arme.libelle', '✅ Libellé arme affiché'),
            ('date_expiration_cin', '✅ Date expiration CIN correcte'),
            ('<th>Arme</th>', '✅ En-tête "Arme" correct'),
            ('d-flex flex-column', '✅ Structure d\'affichage CIN correcte')
        ]
        
        all_good = True
        for check, message in checks:
            if check in contenu:
                print(f"   {message}")
            else:
                print(f"   ❌ {message.replace('✅', 'MANQUE:')}")
                all_good = False
        
        return all_good
        
    except Exception as e:
        print(f"❌ Erreur test : {str(e)}")
        return False

if __name__ == "__main__":
    print("🔧 Correction finale du template CIN et Unité")
    print("=" * 60)
    
    success1 = correction_finale()
    success2 = test_template_final()
    
    print("\n" + "=" * 60)
    if success1 and success2:
        print("🎉 CORRECTION FINALE RÉUSSIE")
        print("✅ Le template affiche maintenant:")
        print("   📋 Numéro de CIN avec statut d'expiration")
        print("   🏢 Libellé complet de l'unité")
        print("   ⚔️ Libellé de l'arme (au lieu de service)")
    else:
        print("❌ CORRECTION FINALE ÉCHOUÉE")
