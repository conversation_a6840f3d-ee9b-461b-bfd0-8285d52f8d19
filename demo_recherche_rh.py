#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Démonstration de la recherche RH fonctionnelle
Lancez ce script et allez sur http://localhost:5001/recherche_simple
"""

from solution_recherche_rh import create_solution_app

if __name__ == "__main__":
    print("=" * 60)
    print("DÉMONSTRATION RECHERCHE RH")
    print("=" * 60)
    print("🚀 Lancement du serveur de démonstration...")
    print("📍 URL: http://localhost:5001/recherche_simple")
    print("📍 API: http://localhost:5001/api/recherche_test")
    print("\n📝 Données de test disponibles:")
    print("   Noms: OUALI, BENALI, BERRADA, IDRISSI, OUAZZANI")
    print("   Matricules: 1056663, 1067602, 1122634")
    print("\n⏹️  Appuyez sur Ctrl+C pour arrêter")
    print("=" * 60)
    
    app = create_solution_app()
    app.run(debug=True, host='0.0.0.0', port=5001)
