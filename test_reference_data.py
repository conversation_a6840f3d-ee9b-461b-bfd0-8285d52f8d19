#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Script de test pour vérifier les données de référence dans le formulaire
"""

import sys
import os
from flask import Flask
from db import db
from rh_models import *

def create_app():
    """Créer l'application Flask pour le contexte de base de données"""
    app = Flask(__name__)
    app.config['SQLALCHEMY_DATABASE_URI'] = 'mysql+pymysql://root:@localhost/gestion_vehicules'
    app.config['SQLALCHEMY_TRACK_MODIFICATIONS'] = False
    db.init_app(app)
    return app

def test_reference_data():
    """Tester les données de référence utilisées dans le formulaire"""
    app = create_app()
    
    with app.app_context():
        try:
            print("🔍 Test des données de référence pour le formulaire...")
            
            # Test 1: États matrimoniaux
            print("\n1. ÉTATS MATRIMONIAUX:")
            print("-" * 40)
            etats_matrimoniaux = ReferentielSituationFamiliale.query.all()
            print(f"Nombre d'états: {len(etats_matrimoniaux)}")
            for etat in etats_matrimoniaux:
                print(f"  - ID: {etat.id_sitfam}, Libellé: '{etat.libelle}'")
            
            # Test 2: Services/Armes
            print("\n2. SERVICES/ARMES:")
            print("-" * 40)
            services = ReferentielArme.query.all()
            print(f"Nombre de services: {len(services)}")
            for service in services:
                print(f"  - ID: {service.id_arme}, Libellé: '{service.libelle}'")
            
            # Test 3: Spécialités
            print("\n3. SPÉCIALITÉS:")
            print("-" * 40)
            specialites = ReferentielSpecialite.query.all()
            print(f"Nombre de spécialités: {len(specialites)}")
            for spec in specialites:
                arme = ReferentielArme.query.get(spec.id_arme)
                arme_nom = arme.libelle if arme else "Inconnue"
                print(f"  - ID: {spec.id_specialite}, Libellé: '{spec.libelle}', Arme: {arme_nom} (ID: {spec.id_arme})")
            
            # Test 4: Degrés de parenté
            print("\n4. DEGRÉS DE PARENTÉ:")
            print("-" * 40)
            liens_parente = ReferentielDegreParente.query.all()
            print(f"Nombre de degrés: {len(liens_parente)}")
            for lien in liens_parente:
                print(f"  - ID: {lien.id_degre}, Libellé: '{lien.libelle}'")
            
            # Test 5: Autres données importantes
            print("\n5. AUTRES DONNÉES DE RÉFÉRENCE:")
            print("-" * 40)
            
            genres = ReferentielGenre.query.all()
            print(f"Genres ({len(genres)}): {[g.libelle for g in genres]}")
            
            categories = ReferentielCategorie.query.all()
            print(f"Catégories ({len(categories)}): {[c.libelle for c in categories]}")
            
            groupes = ReferentielGroupeSanguin.query.all()
            print(f"Groupes sanguins ({len(groupes)}): {[g.libelle for g in groupes]}")
            
            grades = ReferentielGrade.query.all()
            print(f"Grades ({len(grades)}): {[g.libelle for g in grades[:5]]}..." if len(grades) > 5 else f"Grades ({len(grades)}): {[g.libelle for g in grades]}")
            
            unites = ReferentielUnite.query.all()
            print(f"Unités ({len(unites)}): {[u.libelle for u in unites[:3]]}..." if len(unites) > 3 else f"Unités ({len(unites)}): {[u.libelle for u in unites]}")
            
            # Test 6: Vérification des champs utilisés dans le template
            print("\n6. VÉRIFICATION DES CHAMPS TEMPLATE:")
            print("-" * 40)
            
            # Vérifier que les champs correspondent
            print("✅ Champs vérifiés:")
            print("  - État matrimonial: id_sitfam ✓")
            print("  - Service/Arme: id_arme ✓") 
            print("  - Spécialité: id_specialite ✓")
            print("  - Degré parenté: id_degre ✓")
            
            # Test 7: Test de l'API spécialités
            print("\n7. TEST API SPÉCIALITÉS:")
            print("-" * 40)
            artillerie = ReferentielArme.query.filter_by(libelle="Artillerie").first()
            if artillerie:
                specialites_artillerie = ReferentielSpecialite.query.filter_by(id_arme=artillerie.id_arme).all()
                print(f"Spécialités pour Artillerie (ID: {artillerie.id_arme}):")
                for spec in specialites_artillerie:
                    print(f"  - {spec.libelle} (ID: {spec.id_specialite})")
            else:
                print("❌ Service Artillerie non trouvé")
            
            print("\n✅ Test des données de référence terminé avec succès!")
            return True
            
        except Exception as e:
            print(f"❌ Erreur lors du test: {str(e)}")
            return False

if __name__ == "__main__":
    success = test_reference_data()
    sys.exit(0 if success else 1)
