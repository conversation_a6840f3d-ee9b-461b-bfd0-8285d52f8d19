#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Test de l'application web pour vérifier que l'erreur TypeError est corrigée
"""

import sys
import os
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from app import app

def test_page_recherche():
    """Test de la page de recherche"""
    
    with app.test_client() as client:
        try:
            print("🧪 Test de la page de recherche")
            print("=" * 60)
            
            # Test de la page par défaut
            print("📄 Test de la page par défaut...")
            response = client.get('/rh/recherche')
            
            print(f"   Status code: {response.status_code}")
            print(f"   Content-Type: {response.content_type}")
            
            if response.status_code == 200:
                print("   ✅ Page chargée avec succès")
                
                # Vérifier que le contenu contient les éléments attendus
                content = response.get_data(as_text=True)
                
                checks = [
                    ('Recherche Personnel', 'Titre de la page'),
                    ('CIN', 'Colonne CIN'),
                    ('Unité', 'Colonne Unité'),
                    ('Arme', 'Colonne Arme'),
                    ('pagination', 'Pagination'),
                    ('ALAMI', 'Données de test')
                ]
                
                print("\n   🔍 Vérifications du contenu:")
                for texte, description in checks:
                    if texte in content:
                        print(f"      ✅ {description}")
                    else:
                        print(f"      ⚠️ {description} (non trouvé)")
                
                return True
            else:
                print(f"   ❌ Erreur HTTP: {response.status_code}")
                return False
                
        except Exception as e:
            print(f"❌ Erreur lors du test : {str(e)}")
            import traceback
            traceback.print_exc()
            return False

def test_recherche_avec_parametres():
    """Test de recherche avec paramètres"""
    
    with app.test_client() as client:
        try:
            print("\n🧪 Test de recherche avec paramètres")
            print("=" * 60)
            
            # Test de recherche par nom
            print("🔍 Test de recherche par nom 'ALAMI'...")
            response = client.post('/rh/recherche', data={
                'search': 'ALAMI',
                'matricule': '',
                'cin': '',
                'gsm': ''
            })
            
            print(f"   Status code: {response.status_code}")
            
            if response.status_code == 200:
                print("   ✅ Recherche réussie")
                
                content = response.get_data(as_text=True)
                
                # Vérifier les résultats de recherche
                checks = [
                    ('ALAMI', 'Nom recherché'),
                    ('M987500', 'Numéro CIN'),
                    ('10°GAR', 'Unité'),
                    ('Matériel', 'Arme'),
                    ('badge', 'Badge de statut CIN')
                ]
                
                print("\n   📋 Vérifications des résultats:")
                for texte, description in checks:
                    if texte in content:
                        print(f"      ✅ {description}")
                    else:
                        print(f"      ⚠️ {description} (non trouvé)")
                
                return True
            else:
                print(f"   ❌ Erreur HTTP: {response.status_code}")
                return False
                
        except Exception as e:
            print(f"❌ Erreur lors de la recherche : {str(e)}")
            import traceback
            traceback.print_exc()
            return False

def test_pagination():
    """Test de la pagination"""
    
    with app.test_client() as client:
        try:
            print("\n🧪 Test de la pagination")
            print("=" * 60)
            
            # Test de la page 1
            print("📄 Test de la page 1...")
            response = client.get('/rh/recherche?page=1')
            
            print(f"   Status code: {response.status_code}")
            
            if response.status_code == 200:
                content = response.get_data(as_text=True)
                
                # Vérifier les éléments de pagination
                pagination_checks = [
                    ('pagination', 'Contrôles de pagination'),
                    ('page-item', 'Éléments de page'),
                    ('Suivant', 'Bouton suivant'),
                    ('100', 'Nombre d\'éléments par page')
                ]
                
                print("\n   🔢 Vérifications de pagination:")
                for texte, description in pagination_checks:
                    if texte in content:
                        print(f"      ✅ {description}")
                    else:
                        print(f"      ⚠️ {description} (non trouvé)")
                
                # Test de la page 2
                print("\n📄 Test de la page 2...")
                response2 = client.get('/rh/recherche?page=2')
                
                if response2.status_code == 200:
                    print("   ✅ Page 2 accessible")
                    return True
                else:
                    print(f"   ❌ Erreur page 2: {response2.status_code}")
                    return False
            else:
                print(f"   ❌ Erreur HTTP: {response.status_code}")
                return False
                
        except Exception as e:
            print(f"❌ Erreur lors du test pagination : {str(e)}")
            import traceback
            traceback.print_exc()
            return False

if __name__ == "__main__":
    print("🧪 Test de l'application web après correction")
    print("=" * 60)
    
    success1 = test_page_recherche()
    success2 = test_recherche_avec_parametres()
    success3 = test_pagination()
    
    print("\n" + "=" * 60)
    if success1 and success2 and success3:
        print("🎉 TOUS LES TESTS WEB RÉUSSIS")
        print("✅ L'erreur TypeError est complètement corrigée")
        print("📋 Fonctionnalités validées:")
        print("   - Page de recherche accessible")
        print("   - Recherche par nom fonctionnelle")
        print("   - Affichage CIN et unité correct")
        print("   - Pagination fonctionnelle")
        print("   - Aucune erreur TypeError")
    else:
        print("❌ CERTAINS TESTS WEB ONT ÉCHOUÉ")
        print("⚠️ Vérifiez les détails ci-dessus")
