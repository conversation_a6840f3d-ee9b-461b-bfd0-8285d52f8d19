#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Blueprint RH pour l'application de gestion militaire
Routes principales pour la gestion des ressources humaines
"""

from flask import Blueprint, render_template, request, redirect, url_for, flash, jsonify
from datetime import datetime, date
from sqlalchemy import or_, and_, func, desc
from rh_models import *
from db import db
import json

# Création du blueprint RH
rh_bp = Blueprint('rh', __name__, url_prefix='/rh')

# Importer les routes spécialisées
from rh_routes_personnel import *
from rh_routes_famille import *
from rh_routes_medical import *
from rh_routes_absences import *
from rh_routes_mouvements import *

# Les routes de recherche sont définies directement dans ce fichier
print("✅ Routes de recherche RH définies dans rh_blueprint.py")

# ============================================================================
# ROUTES PRINCIPALES
# ============================================================================

@rh_bp.route('/')
@rh_bp.route('/dashboard')
def dashboard():
    """Dashboard principal RH avec statistiques"""
    try:
        # Statistiques générales
        total_personnel = Personnel.query.count()
        total_officiers = Personnel.query.join(ReferentielCategorie).filter(
            ReferentielCategorie.libelle == 'Officier'
        ).count()
        total_officiers_rang = Personnel.query.join(ReferentielCategorie).filter(
            ReferentielCategorie.libelle == 'Officier du rang'
        ).count()
        total_militaires_rang = Personnel.query.join(ReferentielCategorie).filter(
            ReferentielCategorie.libelle == 'Militaire du rang'
        ).count()
        
        # Statistiques par arme
        stats_armes = db.session.query(
            ReferentielArme.libelle,
            db.func.count(Personnel.matricule).label('effectif')
        ).join(Personnel).group_by(ReferentielArme.libelle).all()
        
        # Statistiques par unité (top 10)
        stats_unites = db.session.query(
            ReferentielUnite.libelle,
            db.func.count(Personnel.matricule).label('effectif')
        ).join(Personnel).group_by(ReferentielUnite.libelle).order_by(
            db.func.count(Personnel.matricule).desc()
        ).limit(10).all()
        
        # Personnel récemment engagé (30 derniers jours)
        from datetime import date, timedelta
        date_limite = date.today() - timedelta(days=30)
        nouveaux_engages = Personnel.query.filter(
            Personnel.date_engagement >= date_limite
        ).count()
        
        # Permissions en cours
        permissions_en_cours = Permission.query.filter(
            Permission.date_debut <= date.today(),
            Permission.date_fin >= date.today()
        ).count()
        
        # PTC en cours
        ptc_en_cours = Ptc.query.filter(
            Ptc.date_debut <= date.today(),
            Ptc.date_fin >= date.today()
        ).count()
        
        # Détachements en cours
        detachements_en_cours = Detachement.query.filter(
            Detachement.date_debut <= date.today(),
            Detachement.date_fin >= date.today()
        ).count()

        # Statistiques médicales (pour éviter les erreurs de template)
        total_aptes = SituationMedicale.query.filter(
            SituationMedicale.aptitude_service == 'Apte'
        ).count() if SituationMedicale.query.first() else 0

        total_inaptes = SituationMedicale.query.filter(
            SituationMedicale.aptitude_service == 'Inapte'
        ).count() if SituationMedicale.query.first() else 0

        return render_template('RH/dashboard.html',
                             total_personnel=total_personnel,
                             total_officiers=total_officiers,
                             total_officiers_rang=total_officiers_rang,
                             total_militaires_rang=total_militaires_rang,
                             stats_armes=stats_armes,
                             stats_unites=stats_unites,
                             nouveaux_engages=nouveaux_engages,
                             permissions_en_cours=permissions_en_cours,
                             ptc_en_cours=ptc_en_cours,
                             detachements_en_cours=detachements_en_cours,
                             total_aptes=total_aptes,
                             total_inaptes=total_inaptes)
    except Exception as e:
        flash(f'Erreur lors du chargement du dashboard: {str(e)}', 'error')
        return render_template('rh/dashboard.html')

# ============================================================================
# ROUTES DE RECHERCHE PERSONNEL
# ============================================================================

@rh_bp.route('/recherche', methods=['GET', 'POST'])
def recherche_personnel():
    """Interface de recherche unifiée (simple et avancée)"""
    try:
        # Charger les référentiels pour les filtres avancés
        armes = ReferentielArme.query.all()
        unites = ReferentielUnite.query.all()
        grades = ReferentielGrade.query.all()
        categories = ReferentielCategorie.query.all()
        genres = ReferentielGenre.query.all()
        etats_matrimoniaux = ReferentielSituationFamiliale.query.all()

        # Si c'est une recherche (GET avec paramètres ou POST)
        if request.method == 'POST' or any(request.args.get(param) for param in ['search', 'matricule', 'cin', 'gsm', 'categorie_id', 'grade_id', 'arme_id', 'unite_id', 'specialite_id', 'age_min', 'age_max']):
            return recherche_resultats()

        # Affichage par défaut : liste paginée (100 par page)
        page = request.args.get('page', 1, type=int)
        per_page = 100  # 100 militaires par page

        personnel_paginated = Personnel.query.join(Personnel.arme)\
                                           .join(Personnel.unite)\
                                           .join(Personnel.grade_actuel)\
                                           .join(Personnel.categorie)\
                                           .order_by(Personnel.nom, Personnel.prenom)\
                                           .paginate(page=page, per_page=per_page, error_out=False)

        personnel_defaut = personnel_paginated.items

        # Utiliser directement l'objet de pagination
        personnel_obj = personnel_paginated

        return render_template('RH/recherche_personnel.html',
                             personnel=personnel_obj,
                             search_term='',
                             matricule='',
                             total_resultats=personnel_paginated.total,
                             services=armes,
                             unites=unites,
                             grades=grades,
                             categories=categories,
                             genres=genres,
                             etats_matrimoniaux=etats_matrimoniaux,
                             personnel_defaut=personnel_defaut,
                             affichage_defaut=True,
                             date=date)

    except Exception as e:
        flash(f'Erreur lors du chargement de la page de recherche: {str(e)}', 'error')

        # Créer un objet de pagination vide compatible avec le template
        class EmptyPagination:
            def __init__(self):
                self.items = []
                self.total = 0
                self.pages = 0
                self.page = 1
                self.per_page = 100
                self.has_prev = False
                self.has_next = False
                self.prev_num = None
                self.next_num = None

            def iter_pages(self):
                return []

        return render_template('RH/recherche_personnel.html',
                             personnel=EmptyPagination(),
                             search_term='',
                             matricule='',
                             total_resultats=0,
                             armes=[],
                             unites=[],
                             grades=[],
                             categories=[],
                             date=date,
                             personnel_defaut=[],
                             affichage_defaut=False)

def recherche_resultats():
    """Traitement des recherches simple et avancée - VERSION INTÉGRÉE"""
    try:
        # Récupération des paramètres (GET ou POST)
        if request.method == 'POST':
            data = request.form
        else:
            data = request.args

        # Paramètres de recherche simple
        nom = data.get('nom', '').strip()
        prenom = data.get('prenom', '').strip()
        matricule = data.get('matricule', '').strip()
        grade_id = data.get('grade_id')

        # Paramètres de recherche avancée
        cin = data.get('cin', '').strip()
        gsm = data.get('gsm', '').strip()
        date_naissance = data.get('date_naissance', '').strip()
        genre_id = data.get('genre_id')
        categorie_id = data.get('categorie_id')
        arme_id = data.get('arme_id')
        unite_id = data.get('unite_id')
        situation_familiale_id = data.get('situation_familiale_id')

        # Construction de la requête de base
        query = Personnel.query

        # Application des filtres de recherche simple
        if nom:
            query = query.filter(
                or_(
                    Personnel.nom.ilike(f'%{nom}%'),
                    Personnel.nom_arabe.ilike(f'%{nom}%')
                )
            )

        if prenom:
            query = query.filter(
                or_(
                    Personnel.prenom.ilike(f'%{prenom}%'),
                    Personnel.prenom_arabe.ilike(f'%{prenom}%')
                )
            )

        if matricule:
            query = query.filter(Personnel.matricule.ilike(f'%{matricule}%'))

        if cin:
            query = query.filter(Personnel.numero_cin.ilike(f'%{cin}%'))

        if gsm:
            query = query.filter(Personnel.gsm.ilike(f'%{gsm}%'))

        if arme_id:
            query = query.filter(Personnel.arme_id == arme_id)

        if unite_id:
            query = query.filter(Personnel.unite_id == unite_id)

        if grade_id:
            query = query.filter(Personnel.grade_actuel_id == grade_id)

        if categorie_id:
            query = query.filter(Personnel.categorie_id == categorie_id)

        # Filtres avancés supplémentaires
        if date_naissance:
            query = query.filter(Personnel.date_naissance == date_naissance)

        if genre_id:
            query = query.filter(Personnel.genre_id == genre_id)

        if situation_familiale_id:
            query = query.filter(Personnel.situation_familiale_id == situation_familiale_id)

        # Pagination pour les résultats de recherche
        page = request.args.get('page', 1, type=int)
        per_page = 100  # 100 résultats par page

        # Exécution de la requête avec pagination
        personnel_paginated = query.order_by(Personnel.nom, Personnel.prenom)\
                                  .paginate(page=page, per_page=per_page, error_out=False)

        # Charger les référentiels pour l'affichage
        armes = ReferentielArme.query.all()
        unites = ReferentielUnite.query.all()
        grades = ReferentielGrade.query.all()
        categories = ReferentielCategorie.query.all()
        genres = ReferentielGenre.query.all()
        etats_matrimoniaux = ReferentielSituationFamiliale.query.all()

        # Utiliser directement l'objet de pagination
        personnel_obj = personnel_paginated

        # Retourner les résultats avec le bon format pour le template
        return render_template('RH/recherche_personnel.html',
                             personnel=personnel_obj,
                             nom=nom,
                             prenom=prenom,
                             matricule=matricule,
                             grade_id=grade_id,
                             cin=cin,
                             gsm=gsm,
                             date_naissance=date_naissance,
                             genre_id=genre_id,
                             categorie_id=categorie_id,
                             arme_id=arme_id,
                             unite_id=unite_id,
                             situation_familiale_id=situation_familiale_id,
                             total_resultats=personnel_paginated.total,
                             services=armes,
                             unites=unites,
                             grades=grades,
                             categories=categories,
                             genres=genres,
                             etats_matrimoniaux=etats_matrimoniaux,
                             affichage_defaut=False,
                             date=date)

    except Exception as e:
        # En cas d'erreur, afficher un message et rediriger
        flash(f'Erreur lors de la recherche: {str(e)}', 'error')
        return redirect(url_for('rh.recherche_personnel'))

# Route de recherche déplacée vers rh/routes.py pour éviter les conflits

@rh_bp.route('/api/recherche', methods=['POST'])
def api_recherche_personnel():
    """API de recherche du personnel avec filtres"""
    try:
        data = request.get_json()
        
        # Construction de la requête de base
        query = Personnel.query
        
        # Filtres
        if data.get('matricule'):
            query = query.filter(Personnel.matricule.like(f"%{data['matricule']}%"))
        
        if data.get('nom'):
            query = query.filter(Personnel.nom.like(f"%{data['nom']}%"))
        
        if data.get('prenom'):
            query = query.filter(Personnel.prenom.like(f"%{data['prenom']}%"))
        
        if data.get('arme_id'):
            query = query.filter(Personnel.arme_id == data['arme_id'])
        
        if data.get('unite_id'):
            query = query.filter(Personnel.unite_id == data['unite_id'])
        
        if data.get('grade_id'):
            query = query.filter(Personnel.grade_actuel_id == data['grade_id'])
        
        if data.get('categorie_id'):
            query = query.filter(Personnel.categorie_id == data['categorie_id'])
        
        # Exécution de la requête
        resultats = query.all()
        
        # Formatage des résultats
        personnel_list = []
        for p in resultats:
            personnel_list.append({
                'matricule': p.matricule,
                'nom_complet': p.nom_complet,
                'nom_complet_arabe': p.nom_complet_arabe,
                'arme': p.arme.libelle,
                'unite': p.unite.libelle,
                'grade': p.grade_actuel.libelle,
                'fonction': p.fonction,
                'date_engagement': p.date_engagement.strftime('%d/%m/%Y')
            })
        
        return jsonify({
            'success': True,
            'personnel': personnel_list,
            'total': len(personnel_list)
        })
        
    except Exception as e:
        return jsonify({
            'success': False,
            'error': str(e)
        }), 500

@rh_bp.route('/nouveau_militaire')
def nouveau_militaire():
    """Page d'ajout d'un nouveau militaire"""
    try:
        # Récupérer toutes les données de référence
        genres = ReferentielGenre.query.all()
        categories = ReferentielCategorie.query.all()
        groupes_sanguins = ReferentielGroupeSanguin.query.all()
        armes = ReferentielArme.query.all()
        unites = ReferentielUnite.query.all()
        grades = ReferentielGrade.query.all()
        situations_familiales = ReferentielSituationFamiliale.query.all()
        degres_parente = ReferentielDegreParente.query.all()
        langues = ReferentielLangue.query.all()
        
        return render_template('rh/nouveau_militaire.html',
                             genres=genres,
                             categories=categories,
                             groupes_sanguins=groupes_sanguins,
                             services=armes,
                             specialites=ReferentielSpecialite.query.all(),
                             unites=unites,
                             grades=grades,
                             etats_matrimoniaux=situations_familiales,
                             liens_parente=degres_parente,
                             langues=langues)
    except Exception as e:
        flash(f'Erreur lors du chargement du formulaire: {str(e)}', 'error')
        return render_template('rh/nouveau_militaire.html')

@rh_bp.route('/api/specialites/<int:arme_id>')
def api_specialites_par_arme(arme_id):
    """API pour récupérer les spécialités d'une arme"""
    try:
        specialites = ReferentielSpecialite.query.filter_by(id_arme=arme_id).all()
        specialites_list = [{'id': s.id_specialite, 'libelle': s.libelle} for s in specialites]
        return jsonify({
            'success': True,
            'specialites': specialites_list
        })
    except Exception as e:
        return jsonify({
            'success': False,
            'error': str(e)
        }), 500

@rh_bp.route('/personnel/<matricule>')
def fiche_personnel(matricule):
    """Affichage de la fiche complète d'un personnel"""
    try:
        personnel = Personnel.query.filter_by(matricule=matricule).first()
        if not personnel:
            flash('Personnel non trouvé', 'error')
            return redirect(url_for('rh.recherche_personnel'))

        # Charger toutes les données associées
        conjoint = Conjoint.query.filter_by(matricule=matricule).first()
        enfants = Enfant.query.filter_by(matricule=matricule).all()
        situation_medicale = SituationMedicale.query.filter_by(matricule=matricule).first()
        vaccinations = Vaccination.query.filter_by(matricule=matricule).order_by(desc(Vaccination.date_vaccination)).all()
        ptcs = Ptc.query.filter_by(matricule=matricule).order_by(desc(Ptc.date_ptc)).all()
        permissions = Permission.query.filter_by(matricule=matricule).order_by(desc(Permission.date_debut)).all()
        desertions = Desertion.query.filter_by(matricule=matricule).order_by(desc(Desertion.date_desertion)).all()

        # Charger les mouvements et autres données
        detachements = Detachement.query.filter_by(matricule=matricule).order_by(desc(Detachement.date_debut)).all()
        mutations = MutationFonction.query.filter_by(matricule=matricule).order_by(desc(MutationFonction.date_debut)).all()
        sejours_ops = SejourOps.query.filter_by(matricule=matricule).order_by(desc(SejourOps.date_debut)).all()
        liberations = Liberation.query.filter_by(matricule=matricule).order_by(desc(Liberation.date_liberation)).all()
        avancements = Avancement.query.filter_by(matricule=matricule).order_by(desc(Avancement.date_avancement)).all()
        sanctions = Sanction.query.filter_by(matricule=matricule).order_by(desc(Sanction.date_sanction)).all()

        # Calcul de l'âge
        age = None
        if personnel.date_naissance:
            today = date.today()
            age = today.year - personnel.date_naissance.year - ((today.month, today.day) < (personnel.date_naissance.month, personnel.date_naissance.day))

        return render_template('RH/fiche_personnel_complete.html',
                             militaire=personnel,  # Variable attendue par le template
                             personnel=personnel,  # Pour compatibilité
                             age=age,
                             conjoint=conjoint,
                             enfants=enfants,
                             situation_medicale=situation_medicale,
                             vaccinations=vaccinations,
                             ptcs=ptcs,
                             permissions=permissions,
                             desertions=desertions,
                             detachements=detachements,
                             mutations=mutations,
                             sejours_ops=sejours_ops,
                             liberations=liberations,
                             avancements=avancements,
                             sanctions=sanctions,
                             date=date)
    except Exception as e:
        flash(f'Erreur lors du chargement de la fiche personnel: {str(e)}', 'error')
        return redirect(url_for('rh.recherche_personnel'))

# ============================================================================
# ROUTES DE GESTION
# ============================================================================

@rh_bp.route('/gestion_famille_simple/<matricule>')
def gestion_famille_simple(matricule):
    """Page de modification simple des informations familiales"""
    try:
        personnel = Personnel.query.get_or_404(matricule)
        etats_matrimoniaux = ReferentielSituationFamiliale.query.all()
        genres = ReferentielGenre.query.all()
        conjoint = personnel.conjoint
        return render_template('RH/gestion_famille_simple.html',
                             militaire=personnel,
                             personnel=personnel,
                             etats_matrimoniaux=etats_matrimoniaux,
                             genres=genres,
                             conjoint=conjoint)
    except Exception as e:
        flash(f'Erreur lors du chargement de la gestion familiale: {str(e)}', 'error')
        return redirect(url_for('rh.recherche_personnel'))

@rh_bp.route('/modifier_famille_simple/<matricule>', methods=['POST'])
def modifier_famille_simple(matricule):
    """Traitement de la modification simple des informations familiales"""
    try:
        personnel = Personnel.query.get_or_404(matricule)

        # Récupération des données du formulaire - Personnel
        adresse_parents = request.form.get('adresse_parents', '').strip()
        situation_familiale_id = request.form.get('situation_familiale_id')
        nombre_enfants = request.form.get('nombre_enfants')

        # Mise à jour des données personnel
        personnel.adresse_parents = adresse_parents if adresse_parents else None
        personnel.situation_familiale_id = int(situation_familiale_id) if situation_familiale_id else None
        personnel.nombre_enfants = int(nombre_enfants) if nombre_enfants else 0

        # Récupération des données du conjoint
        conjoint_adresse = request.form.get('conjoint_adresse', '').strip()
        conjoint_adresse_arabe = request.form.get('conjoint_adresse_arabe', '').strip()
        conjoint_gsm = request.form.get('conjoint_gsm', '').strip()
        conjoint_profession = request.form.get('conjoint_profession', '').strip()
        conjoint_profession_arabe = request.form.get('conjoint_profession_arabe', '').strip()
        conjoint_profession_pere = request.form.get('conjoint_profession_pere', '').strip()
        conjoint_profession_mere = request.form.get('conjoint_profession_mere', '').strip()

        # Mise à jour du conjoint si il existe
        if personnel.conjoint:
            conjoint = personnel.conjoint
            if conjoint_adresse:
                conjoint.adresse = conjoint_adresse
            if conjoint_adresse_arabe:
                conjoint.adresse_arabe = conjoint_adresse_arabe
            if conjoint_gsm:
                conjoint.gsm = conjoint_gsm
            if conjoint_profession:
                conjoint.profession = conjoint_profession
            if conjoint_profession_arabe:
                conjoint.profession_arabe = conjoint_profession_arabe
            if conjoint_profession_pere:
                conjoint.profession_pere = conjoint_profession_pere
            if conjoint_profession_mere:
                conjoint.profession_mere = conjoint_profession_mere

        # Récupération des données du nouvel enfant
        enfant_nom_complet = request.form.get('enfant_nom_complet', '').strip()
        enfant_sexe_id = request.form.get('enfant_sexe_id')
        enfant_date_naissance = request.form.get('enfant_date_naissance')
        enfant_lieu_naissance = request.form.get('enfant_lieu_naissance', '').strip()
        enfant_date_deces = request.form.get('enfant_date_deces', '').strip()

        # Ajout du nouvel enfant si les données sont fournies
        if enfant_nom_complet and enfant_sexe_id and enfant_date_naissance:
            # Séparer le nom complet en prénom et nom
            nom_parts = enfant_nom_complet.split(' ', 1)
            prenom = nom_parts[0] if len(nom_parts) > 0 else enfant_nom_complet
            nom = nom_parts[1] if len(nom_parts) > 1 else enfant_nom_complet

            from datetime import datetime
            date_naissance = datetime.strptime(enfant_date_naissance, '%Y-%m-%d').date()

            # Traitement de la date de décès
            date_deces = None
            if enfant_date_deces:
                date_deces = datetime.strptime(enfant_date_deces, '%Y-%m-%d').date()

            nouvel_enfant = Enfant(
                matricule=matricule,
                nom=nom,
                prenom=prenom,
                sexe_id=int(enfant_sexe_id),
                date_naissance=date_naissance,
                lieu_naissance=enfant_lieu_naissance if enfant_lieu_naissance else 'Non spécifié',
                date_deces=date_deces
            )
            db.session.add(nouvel_enfant)

            # Mettre à jour le nombre d'enfants
            personnel.nombre_enfants = len(personnel.enfants) + 1

        # Sauvegarde
        db.session.commit()
        flash('Informations familiales mises à jour avec succès', 'success')

        return redirect(url_for('rh.fiche_personnel', matricule=matricule))

    except Exception as e:
        db.session.rollback()
        flash(f'Erreur lors de la mise à jour: {str(e)}', 'error')
        return redirect(url_for('rh.gestion_famille_simple', matricule=matricule))

@rh_bp.route('/modifier_informations_personnelles/<matricule>', methods=['POST'])
def modifier_informations_personnelles(matricule):
    """Route pour modifier les informations personnelles d'un militaire"""
    try:
        personnel = Personnel.query.get_or_404(matricule)

        # Récupération des données du formulaire
        situation_familiale_id = request.form.get('situation_familiale_id')
        grade_id = request.form.get('grade_id')
        date_nomination = request.form.get('date_nomination')
        unite_id = request.form.get('unite_id')
        date_affectation = request.form.get('date_affectation')
        fonction_id = request.form.get('fonction_id')
        date_prise_fonction = request.form.get('date_prise_fonction')
        gsm = request.form.get('gsm')
        adresse = request.form.get('adresse')

        # Mise à jour des informations personnelles
        if situation_familiale_id:
            personnel.situation_familiale_id = int(situation_familiale_id)

        if grade_id:
            personnel.grade_actuel_id = int(grade_id)
            # Créer un historique de grade si nécessaire
            if date_nomination:
                from datetime import datetime
                date_nom = datetime.strptime(date_nomination, '%Y-%m-%d').date()
                # Logique d'historique des grades à implémenter

        if unite_id:
            personnel.unite_id = int(unite_id)
            # Logique d'historique des unités à implémenter

        if fonction_id:
            personnel.fonction = fonction_id  # À adapter selon le modèle
            # Logique d'historique des fonctions à implémenter

        if gsm:
            personnel.gsm = gsm

        if adresse:
            personnel.residence = adresse

        db.session.commit()
        flash('Informations personnelles mises à jour avec succès', 'success')

    except Exception as e:
        db.session.rollback()
        flash(f'Erreur lors de la mise à jour: {str(e)}', 'error')

    return redirect(url_for('rh.fiche_personnel', matricule=matricule))

@rh_bp.route('/modifier_personnel/<matricule>')
def modifier_personnel(matricule):
    """Page de modification des informations personnelles du militaire"""
    try:
        personnel = Personnel.query.get_or_404(matricule)

        # Charger les données de référence
        etats_matrimoniaux = ReferentielSituationFamiliale.query.all()
        grades = ReferentielGrade.query.order_by(ReferentielGrade.id_grade).all()
        categories = ReferentielCategorie.query.all()
        unites = ReferentielUnite.query.all()
        fonctions = ReferentielSpecialite.query.all()

        return render_template('RH/modifier_personnel.html',
                             militaire=personnel,
                             personnel=personnel,
                             etats_matrimoniaux=etats_matrimoniaux,
                             grades=grades,
                             categories=categories,
                             unites=unites,
                             fonctions=fonctions)
    except Exception as e:
        flash(f'Erreur lors du chargement de la page de modification: {str(e)}', 'error')
        return redirect(url_for('rh.fiche_personnel', matricule=matricule))

@rh_bp.route('/gestion_enfants/<matricule>')
def gestion_enfants(matricule):
    """Page de gestion des enfants"""
    try:
        personnel = Personnel.query.get_or_404(matricule)
        enfants = Enfant.query.filter_by(matricule=matricule).all()
        genres = ReferentielGenre.query.all()
        from datetime import date
        return render_template('RH/gestion_enfants.html',
                             militaire=personnel,
                             personnel=personnel,
                             enfants=enfants,
                             genres=genres,
                             date=date)
    except Exception as e:
        flash(f'Erreur lors du chargement de la gestion des enfants: {str(e)}', 'error')
        return redirect(url_for('rh.recherche_personnel'))

@rh_bp.route('/modifier_enfants/<matricule>', methods=['POST'])
def modifier_enfants(matricule):
    """Traitement de la modification des enfants"""
    try:
        personnel = Personnel.query.get_or_404(matricule)

        # Section 1: Ajouter un nouvel enfant
        nouveau_nom_complet = request.form.get('nouveau_nom_complet', '').strip()
        nouveau_sexe_id = request.form.get('nouveau_sexe_id')
        nouveau_date_naissance = request.form.get('nouveau_date_naissance')
        nouveau_lieu_naissance = request.form.get('nouveau_lieu_naissance', '').strip()
        nouveau_date_deces = request.form.get('nouveau_date_deces', '').strip()

        if nouveau_nom_complet and nouveau_sexe_id and nouveau_date_naissance:
            # Séparer le nom complet en prénom et nom
            nom_parts = nouveau_nom_complet.split(' ', 1)
            prenom = nom_parts[0] if len(nom_parts) > 0 else nouveau_nom_complet
            nom = nom_parts[1] if len(nom_parts) > 1 else nouveau_nom_complet

            from datetime import datetime
            date_naissance = datetime.strptime(nouveau_date_naissance, '%Y-%m-%d').date()

            # Traitement de la date de décès
            date_deces = None
            if nouveau_date_deces:
                date_deces = datetime.strptime(nouveau_date_deces, '%Y-%m-%d').date()

            nouvel_enfant = Enfant(
                matricule=matricule,
                nom=nom,
                prenom=prenom,
                sexe_id=int(nouveau_sexe_id),
                date_naissance=date_naissance,
                lieu_naissance=nouveau_lieu_naissance if nouveau_lieu_naissance else 'Non spécifié',
                date_deces=date_deces
            )
            db.session.add(nouvel_enfant)

            # Mettre à jour le nombre d'enfants
            personnel.nombre_enfants = len(personnel.enfants) + 1

        # Section 2: Modifier les dates de décès des enfants existants
        enfants = Enfant.query.filter_by(matricule=matricule).all()
        for enfant in enfants:
            date_deces_field = f'enfant_{enfant.id_enfant}_date_deces'
            nouvelle_date_deces = request.form.get(date_deces_field, '').strip()

            if nouvelle_date_deces:
                # Mettre à jour avec la nouvelle date de décès
                from datetime import datetime
                enfant.date_deces = datetime.strptime(nouvelle_date_deces, '%Y-%m-%d').date()
            else:
                # Vider la date de décès (marquer comme vivant)
                enfant.date_deces = None

        # Sauvegarde
        db.session.commit()
        flash('Informations des enfants mises à jour avec succès', 'success')

        return redirect(url_for('rh.fiche_personnel', matricule=matricule))

    except Exception as e:
        db.session.rollback()
        flash(f'Erreur lors de la mise à jour: {str(e)}', 'error')
        return redirect(url_for('rh.gestion_enfants', matricule=matricule))

@rh_bp.route('/gestion_famille/<matricule>')
def gestion_famille(matricule):
    """Page de gestion familiale unifiée"""
    try:
        personnel = Personnel.query.get_or_404(matricule)
        genres = ReferentielGenre.query.all()
        return render_template('RH/gestion_famille_unifie.html',
                             militaire=personnel,
                             personnel=personnel,
                             genres=genres)
    except Exception as e:
        flash(f'Erreur lors du chargement de la gestion familiale: {str(e)}', 'error')
        return redirect(url_for('rh.recherche_personnel'))

@rh_bp.route('/gestion_medical/<matricule>')
def gestion_medical(matricule):
    """Page de gestion médicale unifiée"""
    try:
        personnel = Personnel.query.get_or_404(matricule)
        return render_template('RH/gestion_medical_unifie.html',
                             militaire=personnel,
                             personnel=personnel)
    except Exception as e:
        flash(f'Erreur lors du chargement de la gestion médicale: {str(e)}', 'error')
        return redirect(url_for('rh.recherche_personnel'))

@rh_bp.route('/gestion_absences/<matricule>')
def gestion_absences(matricule):
    """Page de gestion des absences unifiée"""
    try:
        personnel = Personnel.query.get_or_404(matricule)
        unites = ReferentielUnite.query.all()
        return render_template('RH/gestion_absences_unifie.html',
                             militaire=personnel,
                             personnel=personnel,
                             unites=unites)
    except Exception as e:
        flash(f'Erreur lors du chargement de la gestion des absences: {str(e)}', 'error')
        return redirect(url_for('rh.recherche_personnel'))

@rh_bp.route('/gestion_mouvements/<matricule>')
def gestion_mouvements(matricule):
    """Page de gestion des mouvements unifiée"""
    try:
        personnel = Personnel.query.get_or_404(matricule)
        armes = ReferentielArme.query.all()
        unites = ReferentielUnite.query.all()
        grades = ReferentielGrade.query.all()
        return render_template('RH/gestion_mouvements_unifie.html',
                             militaire=personnel,
                             personnel=personnel,
                             armes=armes,
                             unites=unites,
                             grades=grades)
    except Exception as e:
        flash(f'Erreur lors du chargement de la gestion des mouvements: {str(e)}', 'error')
        return redirect(url_for('rh.recherche_personnel'))

# ============================================================================
# ROUTES API POUR LES FORMULAIRES
# ============================================================================

@rh_bp.route('/api/ajouter_personnel', methods=['POST'])
def api_ajouter_personnel():
    """API pour ajouter un nouveau personnel"""
    try:
        # Accepter à la fois les données JSON et les données de formulaire
        if request.is_json:
            data = request.get_json()
            is_api_call = True
        else:
            data = request.form.to_dict()
            is_api_call = False

        # Vérifier si le matricule existe déjà
        if Personnel.query.get(data['matricule']):
            return jsonify({
                'success': False,
                'error': 'Ce matricule existe déjà'
            }), 400

        # Créer le nouveau personnel
        personnel = Personnel(
            matricule=data['matricule'],
            nom=data['nom'],
            prenom=data['prenom'],
            nom_arabe=data['nom_arabe'],
            prenom_arabe=data['prenom_arabe'],
            date_naissance=datetime.strptime(data['date_naissance'], '%Y-%m-%d').date(),
            lieu_naissance=data['lieu_naissance'],
            sexe_id=data['sexe_id'],
            categorie_id=data['categorie_id'],
            groupe_sanguin_id=data['groupe_sanguin_id'],
            numero_cin=data['numero_cin'],
            date_delivrance_cin=datetime.strptime(data['date_delivrance_cin'], '%Y-%m-%d').date(),
            date_expiration_cin=datetime.strptime(data['date_expiration_cin'], '%Y-%m-%d').date(),
            gsm=data['gsm'],
            telephone_domicile=data.get('telephone_domicile'),
            taille=float(data['taille']) if data.get('taille') else None,
            lieu_residence=data['lieu_residence'],
            arme_id=data['arme_id'],
            specialite_id=data.get('specialite_id'),
            unite_id=data['unite_id'],
            grade_actuel_id=data['grade_actuel_id'],
            fonction=data['fonction'],
            date_prise_fonction=datetime.strptime(data['date_prise_fonction'], '%Y-%m-%d').date(),
            ccp=data['ccp'],
            compte_bancaire=data.get('compte_bancaire'),
            numero_somme=data['numero_somme'],
            date_engagement=datetime.strptime(data['date_engagement'], '%Y-%m-%d').date(),
            nom_pere=data['nom_pere'],
            prenom_pere=data['prenom_pere'],
            nom_mere=data['nom_mere'],
            prenom_mere=data['prenom_mere'],
            adresse_parents=data['adresse_parents'],
            situation_fam_id=data['situation_fam_id'],
            nombre_enfants=data.get('nombre_enfants'),
            numero_passport=data.get('numero_passport'),
            date_delivrance_passport=datetime.strptime(data['date_delivrance_passport'], '%Y-%m-%d').date() if data.get('date_delivrance_passport') else None,
            date_expiration_passport=datetime.strptime(data['date_expiration_passport'], '%Y-%m-%d').date() if data.get('date_expiration_passport') else None,
            gsm_urgence=data['gsm_urgence'],
            degre_parente_id=data['degre_parente_id']
        )

        db.session.add(personnel)

        # Ajouter les langues
        if data.get('langues'):
            for langue_id in data['langues']:
                personnel_langue = PersonnelLangue(
                    matricule=data['matricule'],
                    langue_id=langue_id
                )
                db.session.add(personnel_langue)

        db.session.commit()

        if is_api_call:
            return jsonify({
                'success': True,
                'message': 'Personnel ajouté avec succès',
                'matricule': data['matricule']
            })
        else:
            flash('Personnel ajouté avec succès', 'success')
            return redirect(url_for('rh.nouveau_militaire'))

    except Exception as e:
        db.session.rollback()
        if is_api_call:
            return jsonify({
                'success': False,
                'error': str(e)
            }), 500
        else:
            flash(f'Erreur lors de l\'ajout du personnel : {str(e)}', 'error')
            return redirect(url_for('rh.nouveau_militaire'))

@rh_bp.route('/api/ajouter_conjoint', methods=['POST'])
def api_ajouter_conjoint():
    """API pour ajouter un conjoint"""
    try:
        data = request.get_json()

        # Vérifier si le personnel existe
        personnel = Personnel.query.get(data['matricule'])
        if not personnel:
            return jsonify({
                'success': False,
                'error': 'Personnel non trouvé'
            }), 404

        # Vérifier si un conjoint existe déjà
        if personnel.conjoint:
            return jsonify({
                'success': False,
                'error': 'Un conjoint existe déjà pour ce personnel'
            }), 400

        # Créer le conjoint
        conjoint = Conjoint(
            matricule=data['matricule'],
            nom=data['nom'],
            prenom=data['prenom'],
            nom_arabe=data['nom_arabe'],
            prenom_arabe=data['prenom_arabe'],
            date_naissance=datetime.strptime(data['date_naissance'], '%Y-%m-%d').date(),
            lieu_naissance=data['lieu_naissance'],
            lieu_naissance_arabe=data['lieu_naissance_arabe'],
            adresse=data['adresse'],
            adresse_arabe=data['adresse_arabe'],
            date_mariage=datetime.strptime(data['date_mariage'], '%Y-%m-%d').date(),
            lieu_mariage=data['lieu_mariage'],
            profession=data['profession'],
            profession_arabe=data['profession_arabe'],
            numero_cin=data['numero_cin'],
            gsm=data['gsm'],
            nom_pere=data['nom_pere'],
            prenom_pere=data['prenom_pere'],
            nom_arabe_pere=data['nom_arabe_pere'],
            prenom_arabe_pere=data['prenom_arabe_pere'],
            nom_mere=data['nom_mere'],
            prenom_mere=data['prenom_mere'],
            nom_arabe_mere=data['nom_arabe_mere'],
            prenom_arabe_mere=data['prenom_arabe_mere'],
            profession_pere=data['profession_pere'],
            profession_mere=data['profession_mere']
        )

        db.session.add(conjoint)
        db.session.commit()

        return jsonify({
            'success': True,
            'message': 'Conjoint ajouté avec succès'
        })

    except Exception as e:
        db.session.rollback()
        return jsonify({
            'success': False,
            'error': str(e)
        }), 500

@rh_bp.route('/api/ajouter_enfant', methods=['POST'])
def api_ajouter_enfant():
    """API pour ajouter un enfant"""
    try:
        data = request.get_json()

        # Vérifier si le personnel existe
        personnel = Personnel.query.get(data['matricule'])
        if not personnel:
            return jsonify({
                'success': False,
                'error': 'Personnel non trouvé'
            }), 404

        # Créer l'enfant
        enfant = Enfant(
            matricule=data['matricule'],
            nom=data['nom'],
            prenom=data['prenom'],
            sexe_id=data['sexe_id'],
            date_naissance=datetime.strptime(data['date_naissance'], '%Y-%m-%d').date(),
            lieu_naissance=data['lieu_naissance'],
            date_deces=datetime.strptime(data['date_deces'], '%Y-%m-%d').date() if data.get('date_deces') else None
        )

        db.session.add(enfant)

        # Mettre à jour le nombre d'enfants du personnel
        personnel.nombre_enfants = len(personnel.enfants) + 1

        db.session.commit()

        return jsonify({
            'success': True,
            'message': 'Enfant ajouté avec succès'
        })

    except Exception as e:
        db.session.rollback()
        return jsonify({
            'success': False,
            'error': str(e)
        }), 500

# ============================================================================
# IMPORT DES ROUTES SPÉCIALISÉES
# ============================================================================

# Importer les routes spécialisées pour les ajouter au blueprint
try:
    from rh_routes_personnel import *
    from rh_routes_famille import *
    from rh_routes_medical import *
    from rh_routes_absences import *
    from rh_routes_mouvements import *
    print("✅ Routes spécialisées RH importées avec succès")
except ImportError as e:
    print(f"⚠️ Erreur lors de l'import des routes spécialisées: {e}")
