#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Audit complet des champs RH - Vérification de cohérence entre modèles, templates et formulaires
"""

import os
import re
from collections import defaultdict

def analyser_modeles_donnees():
    """Analyse les modèles de données dans rh_models.py"""
    print("=" * 80)
    print("ANALYSE DES MODÈLES DE DONNÉES (rh_models.py)")
    print("=" * 80)
    
    # Lire le fichier des modèles
    with open('rh_models.py', 'r', encoding='utf-8') as f:
        contenu = f.read()
    
    # Extraire les champs de la table Personnel
    print("\n1. CHAMPS DE LA TABLE PERSONNEL:")
    print("-" * 50)
    
    # Trouver la classe Personnel
    match_personnel = re.search(r'class Personnel\(db\.Model\):(.*?)(?=class|\Z)', contenu, re.DOTALL)
    if match_personnel:
        personnel_content = match_personnel.group(1)
        
        # Extraire tous les champs db.Column
        champs_personnel = re.findall(r'(\w+)\s*=\s*db\.Column\((.*?)\)', personnel_content, re.DOTALL)
        
        champs_obligatoires = []
        champs_optionnels = []
        champs_references = []
        
        for nom_champ, definition in champs_personnel:
            if 'nullable=False' in definition or 'primary_key=True' in definition:
                champs_obligatoires.append(nom_champ)
            elif 'nullable=True' in definition:
                champs_optionnels.append(nom_champ)
            else:
                # Par défaut nullable=False
                champs_obligatoires.append(nom_champ)
            
            if 'ForeignKey' in definition:
                champs_references.append(nom_champ)
        
        print(f"   Champs obligatoires ({len(champs_obligatoires)}):")
        for champ in sorted(champs_obligatoires):
            print(f"     - {champ}")
        
        print(f"\n   Champs optionnels ({len(champs_optionnels)}):")
        for champ in sorted(champs_optionnels):
            print(f"     - {champ}")
        
        print(f"\n   Champs de référence ({len(champs_references)}):")
        for champ in sorted(champs_references):
            print(f"     - {champ}")
    
    # Analyser les autres tables importantes
    tables_importantes = [
        'Conjoint', 'Enfant', 'SituationMedicale', 'Vaccination', 
        'Ptc', 'Permission', 'Desertion', 'Detachement', 
        'MutationFonction', 'SejourOps', 'Liberation', 'Avancement', 'Sanction'
    ]
    
    print(f"\n2. AUTRES TABLES IMPORTANTES ({len(tables_importantes)}):")
    print("-" * 50)
    
    for table in tables_importantes:
        match_table = re.search(rf'class {table}\(db\.Model\):(.*?)(?=class|\Z)', contenu, re.DOTALL)
        if match_table:
            table_content = match_table.group(1)
            champs_table = re.findall(r'(\w+)\s*=\s*db\.Column\((.*?)\)', table_content, re.DOTALL)
            print(f"   {table}: {len(champs_table)} champs")
        else:
            print(f"   {table}: ❌ NON TROUVÉE")
    
    # Analyser les tables de référence
    tables_reference = [
        'ReferentielGenre', 'ReferentielCategorie', 'ReferentielGroupeSanguin',
        'ReferentielArme', 'ReferentielSpecialite', 'ReferentielUnite',
        'ReferentielGrade', 'ReferentielSituationFamiliale', 'ReferentielDegreParente',
        'ReferentielLangue'
    ]
    
    print(f"\n3. TABLES DE RÉFÉRENCE ({len(tables_reference)}):")
    print("-" * 50)
    
    for table in tables_reference:
        match_table = re.search(rf'class {table}\(db\.Model\):(.*?)(?=class|\Z)', contenu, re.DOTALL)
        if match_table:
            table_content = match_table.group(1)
            champs_table = re.findall(r'(\w+)\s*=\s*db\.Column\((.*?)\)', table_content, re.DOTALL)
            print(f"   {table}: {len(champs_table)} champs")
        else:
            print(f"   {table}: ❌ NON TROUVÉE")
    
    return {
        'personnel_obligatoires': champs_obligatoires,
        'personnel_optionnels': champs_optionnels,
        'personnel_references': champs_references
    }

def analyser_architecture_reference():
    """Analyse l'architecture de référence dans architecture_rh.md"""
    print("\n" + "=" * 80)
    print("ANALYSE DE L'ARCHITECTURE DE RÉFÉRENCE (architecture_rh.md)")
    print("=" * 80)
    
    try:
        with open('architecture_rh.md', 'r', encoding='utf-8') as f:
            contenu = f.read()
        
        # Extraire les champs de la table personnel selon l'architecture
        print("\n1. CHAMPS PERSONNEL SELON L'ARCHITECTURE:")
        print("-" * 50)
        
        # Trouver la section personnel
        match_personnel = re.search(r'### 2\.1 personnel(.*?)(?=### 2\.2|\Z)', contenu, re.DOTALL)
        if match_personnel:
            personnel_section = match_personnel.group(1)
            
            # Extraire les champs
            champs_arch = re.findall(r'- \*\*(\w+)\*\*.*?(?=NOT NULL|NULL|PRIMARY KEY)', personnel_section)
            
            print(f"   Champs trouvés dans l'architecture ({len(champs_arch)}):")
            for champ in champs_arch:
                print(f"     - {champ}")
        
        return champs_arch
    except FileNotFoundError:
        print("   ❌ Fichier architecture_rh.md non trouvé")
        return []

def analyser_templates_affichage():
    """Analyse les templates d'affichage"""
    print("\n" + "=" * 80)
    print("ANALYSE DES TEMPLATES D'AFFICHAGE")
    print("=" * 80)
    
    templates_a_analyser = [
        'templates/rh/recherche_personnel.html',
        'templates/rh/fiche_personnel_complete.html',
        'templates/rh/personnel/fiche.html'
    ]
    
    champs_affiches = set()
    
    for template_path in templates_a_analyser:
        print(f"\n1. ANALYSE DE {template_path}:")
        print("-" * 50)
        
        try:
            with open(template_path, 'r', encoding='utf-8') as f:
                contenu = f.read()
            
            # Chercher les références aux champs militaire.xxx
            champs_template = re.findall(r'militaire\.(\w+)', contenu)
            champs_personnel = re.findall(r'personnel\.(\w+)', contenu)
            
            champs_trouves = set(champs_template + champs_personnel)
            champs_affiches.update(champs_trouves)
            
            print(f"   Champs affichés ({len(champs_trouves)}):")
            for champ in sorted(champs_trouves):
                print(f"     - {champ}")
                
        except FileNotFoundError:
            print(f"   ❌ Template {template_path} non trouvé")
    
    print(f"\n2. RÉSUMÉ DES CHAMPS AFFICHÉS:")
    print("-" * 50)
    print(f"   Total unique: {len(champs_affiches)}")
    for champ in sorted(champs_affiches):
        print(f"     - {champ}")
    
    return champs_affiches

def analyser_formulaires_ajout():
    """Analyse les formulaires d'ajout"""
    print("\n" + "=" * 80)
    print("ANALYSE DES FORMULAIRES D'AJOUT")
    print("=" * 80)
    
    formulaires_a_analyser = [
        'templates/rh/nouveau_militaire.html',
        'templates/rh/personnel/nouveau.html'
    ]
    
    champs_formulaires = set()
    
    for formulaire_path in formulaires_a_analyser:
        print(f"\n1. ANALYSE DE {formulaire_path}:")
        print("-" * 50)
        
        try:
            with open(formulaire_path, 'r', encoding='utf-8') as f:
                contenu = f.read()
            
            # Chercher les champs name="xxx"
            champs_name = re.findall(r'name="(\w+)"', contenu)
            champs_trouves = set(champs_name)
            champs_formulaires.update(champs_trouves)
            
            print(f"   Champs de formulaire ({len(champs_trouves)}):")
            for champ in sorted(champs_trouves):
                print(f"     - {champ}")
                
        except FileNotFoundError:
            print(f"   ❌ Formulaire {formulaire_path} non trouvé")
    
    print(f"\n2. RÉSUMÉ DES CHAMPS DE FORMULAIRES:")
    print("-" * 50)
    print(f"   Total unique: {len(champs_formulaires)}")
    for champ in sorted(champs_formulaires):
        print(f"     - {champ}")
    
    return champs_formulaires

def comparer_coherence(modeles_data, champs_affiches, champs_formulaires, champs_arch):
    """Compare la cohérence entre tous les éléments"""
    print("\n" + "=" * 80)
    print("ANALYSE DE COHÉRENCE")
    print("=" * 80)
    
    # Tous les champs du modèle Personnel
    tous_champs_modele = set(modeles_data['personnel_obligatoires'] + modeles_data['personnel_optionnels'])
    
    print(f"\n1. STATISTIQUES:")
    print("-" * 50)
    print(f"   Champs dans le modèle Personnel: {len(tous_champs_modele)}")
    print(f"   Champs dans l'architecture: {len(champs_arch)}")
    print(f"   Champs affichés dans les templates: {len(champs_affiches)}")
    print(f"   Champs dans les formulaires: {len(champs_formulaires)}")
    
    print(f"\n2. CHAMPS MANQUANTS DANS LES TEMPLATES:")
    print("-" * 50)
    manquants_templates = tous_champs_modele - champs_affiches
    if manquants_templates:
        for champ in sorted(manquants_templates):
            print(f"   ❌ {champ}")
    else:
        print("   ✅ Tous les champs du modèle sont affichés")
    
    print(f"\n3. CHAMPS MANQUANTS DANS LES FORMULAIRES:")
    print("-" * 50)
    manquants_formulaires = set(modeles_data['personnel_obligatoires']) - champs_formulaires
    if manquants_formulaires:
        for champ in sorted(manquants_formulaires):
            print(f"   ❌ {champ} (OBLIGATOIRE)")
    else:
        print("   ✅ Tous les champs obligatoires sont dans les formulaires")
    
    print(f"\n4. CHAMPS EN TROP DANS LES TEMPLATES:")
    print("-" * 50)
    en_trop_templates = champs_affiches - tous_champs_modele
    if en_trop_templates:
        for champ in sorted(en_trop_templates):
            print(f"   ⚠️  {champ}")
    else:
        print("   ✅ Aucun champ en trop dans les templates")
    
    print(f"\n5. CHAMPS EN TROP DANS LES FORMULAIRES:")
    print("-" * 50)
    en_trop_formulaires = champs_formulaires - tous_champs_modele
    if en_trop_formulaires:
        for champ in sorted(en_trop_formulaires):
            print(f"   ⚠️  {champ}")
    else:
        print("   ✅ Aucun champ en trop dans les formulaires")

if __name__ == "__main__":
    print("AUDIT COMPLET DES CHAMPS RH")
    print("=" * 80)
    
    # 1. Analyser les modèles
    modeles_data = analyser_modeles_donnees()
    
    # 2. Analyser l'architecture de référence
    champs_arch = analyser_architecture_reference()
    
    # 3. Analyser les templates
    champs_affiches = analyser_templates_affichage()
    
    # 4. Analyser les formulaires
    champs_formulaires = analyser_formulaires_ajout()
    
    # 5. Comparer la cohérence
    comparer_coherence(modeles_data, champs_affiches, champs_formulaires, champs_arch)
    
    print("\n" + "=" * 80)
    print("AUDIT TERMINÉ")
    print("=" * 80)
