from app import db
from sqlalchemy import text

def allow_null_taille():
    with db.engine.connect() as connection:
        # Modifier la colonne taille pour permettre NULL
        connection.execute(text("ALTER TABLE personnel MODIFY COLUMN taille FLOAT NULL;"))
        connection.commit()

if __name__ == '__main__':
    try:
        allow_null_taille()
        print("La colonne 'taille' a été modifiée avec succès pour accepter les valeurs NULL")
    except Exception as e:
        print(f"Erreur lors de la modification de la table : {str(e)}")
