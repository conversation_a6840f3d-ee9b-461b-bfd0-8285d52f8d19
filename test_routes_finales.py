#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Test final des routes après suppression des conflits
"""

import requests

def test_routes_finales():
    """Test des routes finales après nettoyage"""
    base_url = "http://localhost:3000"
    
    print("🔧 Test des routes après suppression des conflits...")
    
    try:
        # Test 1: Route de recherche
        print("\n1. Test route de recherche:")
        response = requests.get(f"{base_url}/rh/recherche")
        
        if response.status_code == 200:
            print("✅ Route /rh/recherche fonctionne")
            
            # Vérifier les suggestions
            html = response.text
            if 'Artillerie' in html and 'Célibataire' in html:
                print("  ✅ Suggestions présentes dans la recherche")
            else:
                print("  ❌ Suggestions manquantes dans la recherche")
        else:
            print(f"❌ Route /rh/recherche: Erreur {response.status_code}")
        
        # Test 2: Route d'ajout de militaire
        print("\n2. Test route d'ajout de militaire:")
        response = requests.get(f"{base_url}/rh/nouveau_militaire")
        
        if response.status_code == 200:
            print("✅ Route /rh/nouveau_militaire fonctionne")
            
            # Vérifier les suggestions
            html = response.text
            if 'Artillerie' in html and 'Célibataire' in html:
                print("  ✅ Suggestions présentes dans l'ajout")
            else:
                print("  ❌ Suggestions manquantes dans l'ajout")
        else:
            print(f"❌ Route /rh/nouveau_militaire: Erreur {response.status_code}")
        
        # Test 3: API spécialités
        print("\n3. Test API spécialités:")
        response = requests.get(f"{base_url}/rh/api/specialites/1")
        
        if response.status_code == 200:
            data = response.json()
            if data.get('success') and data.get('specialites'):
                print(f"✅ API spécialités fonctionne: {len(data['specialites'])} spécialité(s)")
            else:
                print("❌ API spécialités: Structure incorrecte")
        else:
            print(f"❌ API spécialités: Erreur {response.status_code}")
        
        # Test 4: Test de recherche avec paramètres
        print("\n4. Test recherche avec paramètres:")
        params = {'search': 'OUALI', 'service_id': '1'}
        response = requests.get(f"{base_url}/rh/recherche", params=params)
        
        if response.status_code == 200:
            print("✅ Recherche avec paramètres fonctionne")
        else:
            print(f"❌ Recherche avec paramètres: Erreur {response.status_code}")
        
        print("\n✅ Test des routes terminé!")
        
        print("\n🧹 Résumé du nettoyage effectué:")
        print("  ✅ Routes en conflit supprimées dans rh/routes.py:")
        print("    - @rh_bp.route('/recherche') [SUPPRIMÉE]")
        print("    - def recherche_resultats() [SUPPRIMÉE]")
        print("    - @rh_bp.route('/nouveau') [SUPPRIMÉE]")
        print("    - @rh_bp.route('/nouveau', methods=['POST']) [SUPPRIMÉE]")
        print("    - Code orphelin supprimé")
        
        print("\n  ✅ Routes fonctionnelles conservées dans rh_blueprint.py:")
        print("    - @rh_bp.route('/recherche') [ACTIVE]")
        print("    - def recherche_resultats() [ACTIVE]")
        print("    - @rh_bp.route('/nouveau_militaire') [ACTIVE]")
        print("    - API spécialités [ACTIVE]")
        
        print("\n🎯 Résultat:")
        print("  ✅ Plus de conflits de routes")
        print("  ✅ Suggestions fonctionnelles dans les deux interfaces")
        print("  ✅ Données de référence cohérentes avec architecture_rh.md")
        
        return True
        
    except requests.exceptions.ConnectionError:
        print("❌ Impossible de se connecter au serveur. Assurez-vous que l'application Flask est en cours d'exécution sur le port 3000.")
        return False
    except Exception as e:
        print(f"❌ Erreur lors du test: {str(e)}")
        return False

if __name__ == "__main__":
    success = test_routes_finales()
    if success:
        print("\n🎉 NETTOYAGE RÉUSSI!")
        print("L'application utilise maintenant uniquement les routes fonctionnelles")
        print("sans conflits ni incohérences.")
    else:
        print("\n❌ Certains tests ont échoué.")
