#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Script de diagnostic pour la recherche RH
Vérifie la connexion à la base de données et les données existantes
"""

from flask import Flask
from db import db, init_app
from rh_models import *
from sqlalchemy import text

def create_app():
    """Créer l'application Flask pour les tests"""
    app = Flask(__name__)
    init_app(app)
    return app

def diagnostic_complet():
    """Diagnostic complet de la base de données RH"""
    app = create_app()
    
    with app.app_context():
        print("=" * 60)
        print("DIAGNOSTIC DE LA BASE DE DONNÉES RH")
        print("=" * 60)
        
        # 1. Test de connexion
        print("\n1. TEST DE CONNEXION À LA BASE DE DONNÉES")
        print("-" * 40)
        try:
            # Test de connexion simple
            result = db.session.execute(text("SELECT 1"))
            print("✅ Connexion à MySQL réussie")
        except Exception as e:
            print(f"❌ Erreur de connexion: {e}")
            return
        
        # 2. Vérification des tables
        print("\n2. VÉRIFICATION DES TABLES")
        print("-" * 40)
        
        tables_rh = [
            'referentiel_genre', 'referentiel_categorie', 'referentiel_groupe_sanguin',
            'referentiel_arme', 'referentiel_specialite', 'referentiel_unite',
            'referentiel_grade', 'referentiel_situation_familiale', 'referentiel_degre_parente',
            'referentiel_langue', 'personnel', 'personnel_langue', 'conjoint',
            'enfant', 'situation_medicale', 'vaccination', 'ptc', 'permission',
            'desertion', 'detachement', 'mutation_fonction', 'sejour_ops',
            'liberation', 'avancement', 'sanction'
        ]
        
        for table in tables_rh:
            try:
                result = db.session.execute(text(f"SELECT COUNT(*) FROM {table}"))
                count = result.scalar()
                print(f"✅ {table}: {count} enregistrement(s)")
            except Exception as e:
                print(f"❌ {table}: Table n'existe pas ou erreur - {e}")
        
        # 3. Vérification spécifique du personnel
        print("\n3. ANALYSE DE LA TABLE PERSONNEL")
        print("-" * 40)
        
        try:
            # Compter le personnel total
            total_personnel = Personnel.query.count()
            print(f"Total personnel: {total_personnel}")
            
            if total_personnel > 0:
                # Afficher quelques exemples
                print("\nExemples de personnel (5 premiers):")
                personnel_exemples = Personnel.query.limit(5).all()
                for p in personnel_exemples:
                    print(f"  - Matricule: {p.matricule}, Nom: {p.nom} {p.prenom}")
                
                # Test de recherche
                print("\n4. TEST DE RECHERCHE")
                print("-" * 40)
                
                # Test recherche par nom
                premier_personnel = personnel_exemples[0] if personnel_exemples else None
                if premier_personnel:
                    nom_test = premier_personnel.nom
                    print(f"Test recherche par nom '{nom_test}':")
                    
                    resultats = Personnel.query.filter(
                        Personnel.nom.ilike(f'%{nom_test}%')
                    ).all()
                    print(f"  Résultats trouvés: {len(resultats)}")
                    
                    # Test recherche par matricule
                    matricule_test = premier_personnel.matricule
                    print(f"Test recherche par matricule '{matricule_test}':")
                    
                    resultats = Personnel.query.filter(
                        Personnel.matricule.ilike(f'%{matricule_test}%')
                    ).all()
                    print(f"  Résultats trouvés: {len(resultats)}")
            else:
                print("❌ Aucun personnel trouvé dans la base de données")
                
        except Exception as e:
            print(f"❌ Erreur lors de l'analyse du personnel: {e}")
        
        # 4. Vérification des référentiels
        print("\n5. VÉRIFICATION DES RÉFÉRENTIELS")
        print("-" * 40)
        
        try:
            armes = ReferentielArme.query.count()
            unites = ReferentielUnite.query.count()
            grades = ReferentielGrade.query.count()
            categories = ReferentielCategorie.query.count()
            
            print(f"Armes: {armes}")
            print(f"Unités: {unites}")
            print(f"Grades: {grades}")
            print(f"Catégories: {categories}")
            
            if armes == 0 or unites == 0 or grades == 0 or categories == 0:
                print("⚠️  Certains référentiels sont vides - cela peut causer des erreurs")
                
        except Exception as e:
            print(f"❌ Erreur lors de la vérification des référentiels: {e}")

def test_recherche_specifique():
    """Test spécifique de la fonction de recherche"""
    app = create_app()
    
    with app.app_context():
        print("\n" + "=" * 60)
        print("TEST SPÉCIFIQUE DE LA RECHERCHE")
        print("=" * 60)
        
        try:
            # Test avec différents critères
            print("\n1. Test recherche vide (tous les résultats):")
            tous_resultats = Personnel.query.limit(10).all()
            print(f"   Résultats: {len(tous_resultats)}")
            
            if tous_resultats:
                premier = tous_resultats[0]
                
                print(f"\n2. Test recherche par nom exact '{premier.nom}':")
                resultats_nom = Personnel.query.filter(
                    Personnel.nom == premier.nom
                ).all()
                print(f"   Résultats: {len(resultats_nom)}")
                
                print(f"\n3. Test recherche par nom partiel '{premier.nom[:3]}':")
                resultats_partiel = Personnel.query.filter(
                    Personnel.nom.ilike(f'%{premier.nom[:3]}%')
                ).all()
                print(f"   Résultats: {len(resultats_partiel)}")
                
                print(f"\n4. Test recherche par matricule '{premier.matricule}':")
                resultats_matricule = Personnel.query.filter(
                    Personnel.matricule == premier.matricule
                ).all()
                print(f"   Résultats: {len(resultats_matricule)}")
                
        except Exception as e:
            print(f"❌ Erreur lors du test de recherche: {e}")

if __name__ == "__main__":
    diagnostic_complet()
    test_recherche_specifique()
