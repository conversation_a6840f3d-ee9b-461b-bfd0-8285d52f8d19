#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Test simple de la recherche intégrée
"""

from flask import Flask, render_template_string
from db import db, init_app
from rh_models import *
from rh_blueprint import rh_bp

def test_recherche_simple():
    """Test simple de la recherche"""
    app = Flask(__name__)
    app.secret_key = 'test_key'
    init_app(app)
    app.register_blueprint(rh_bp)
    
    # Template simple pour tester
    template_simple = """
    <!DOCTYPE html>
    <html>
    <head>
        <title>Test Recherche</title>
        <style>
            body { font-family: Arial, sans-serif; margin: 20px; }
            .result { border: 1px solid #ddd; padding: 10px; margin: 5px 0; }
            .search-form { background: #f5f5f5; padding: 15px; margin-bottom: 20px; }
            .search-form input { margin: 5px; padding: 5px; }
            .search-form button { background: #007bff; color: white; padding: 8px 15px; border: none; cursor: pointer; }
        </style>
    </head>
    <body>
        <h1>Test de Recherche Personnel</h1>
        
        <div class="search-form">
            <form method="GET">
                <input type="text" name="search" placeholder="Nom/Prénom" value="{{ search_term or '' }}">
                <input type="text" name="matricule" placeholder="Matricule" value="{{ matricule or '' }}">
                <button type="submit">Rechercher</button>
                <a href="?" style="margin-left: 10px;">Réinitialiser</a>
            </form>
        </div>
        
        <h2>Résultats ({{ total_resultats }} trouvé(s))</h2>
        
        {% if personnel and personnel.items %}
            {% for militaire in personnel.items %}
            <div class="result">
                <strong>{{ militaire.matricule }}</strong> - 
                {{ militaire.nom }} {{ militaire.prenom }}
                {% if militaire.nom_arabe %}({{ militaire.nom_arabe }} {{ militaire.prenom_arabe }}){% endif %}
                <br>
                <small>
                    {% if militaire.arme %}Arme: {{ militaire.arme.libelle }}{% endif %}
                    {% if militaire.unite %} | Unité: {{ militaire.unite.libelle }}{% endif %}
                    {% if militaire.grade_actuel %} | Grade: {{ militaire.grade_actuel.libelle }}{% endif %}
                </small>
            </div>
            {% endfor %}
        {% else %}
            <p>Aucun résultat trouvé.</p>
        {% endif %}
        
        <hr>
        <p><a href="/rh/">Retour au dashboard</a></p>
    </body>
    </html>
    """
    
    @app.route('/test_recherche')
    def test_recherche():
        """Route de test simple"""
        try:
            # Récupération des paramètres
            search = request.args.get('search', '').strip()
            matricule = request.args.get('matricule', '').strip()
            
            # Construction de la requête
            query = Personnel.query
            
            if search:
                query = query.filter(
                    or_(
                        Personnel.nom.ilike(f'%{search}%'),
                        Personnel.prenom.ilike(f'%{search}%'),
                        Personnel.nom_arabe.ilike(f'%{search}%'),
                        Personnel.prenom_arabe.ilike(f'%{search}%')
                    )
                )
            
            if matricule:
                query = query.filter(Personnel.matricule.ilike(f'%{matricule}%'))
            
            # Exécution de la requête
            resultats = query.order_by(Personnel.nom, Personnel.prenom).limit(50).all()
            
            # Créer un objet simple pour les données
            class PersonnelData:
                def __init__(self, items):
                    self.items = items
                    self.total = len(items)
            
            personnel_obj = PersonnelData(resultats)
            
            return render_template_string(template_simple,
                                        personnel=personnel_obj,
                                        search_term=search,
                                        matricule=matricule,
                                        total_resultats=len(resultats))
                                        
        except Exception as e:
            return f"Erreur: {str(e)}"
    
    with app.test_client() as client:
        print("=" * 60)
        print("TEST SIMPLE DE LA RECHERCHE INTÉGRÉE")
        print("=" * 60)
        
        # Test 1: Page de test simple
        print("\n1. Test de la page de test simple:")
        try:
            response = client.get('/test_recherche')
            print(f"   Status: {response.status_code}")
            if response.status_code == 200:
                content = response.data.decode()
                if 'Test de Recherche Personnel' in content:
                    print("   ✅ Page de test accessible")
                else:
                    print("   ⚠️  Contenu inattendu")
            else:
                print(f"   ❌ Erreur: {response.status_code}")
        except Exception as e:
            print(f"   ❌ Exception: {e}")
        
        # Test 2: Recherche par nom
        print("\n2. Test recherche par nom 'OUALI':")
        try:
            response = client.get('/test_recherche?search=OUALI')
            print(f"   Status: {response.status_code}")
            if response.status_code == 200:
                content = response.data.decode()
                if 'OUALI' in content and 'DRISS' in content:
                    print("   ✅ Résultats trouvés pour OUALI")
                    # Compter les résultats
                    count = content.count('<div class="result">')
                    print(f"   ✅ {count} résultat(s) affiché(s)")
                else:
                    print("   ⚠️  Résultats non trouvés")
            else:
                print(f"   ❌ Erreur: {response.status_code}")
        except Exception as e:
            print(f"   ❌ Exception: {e}")
        
        # Test 3: Recherche par matricule
        print("\n3. Test recherche par matricule '1056663':")
        try:
            response = client.get('/test_recherche?matricule=1056663')
            print(f"   Status: {response.status_code}")
            if response.status_code == 200:
                content = response.data.decode()
                if '1056663' in content:
                    print("   ✅ Matricule trouvé")
                else:
                    print("   ⚠️  Matricule non trouvé")
            else:
                print(f"   ❌ Erreur: {response.status_code}")
        except Exception as e:
            print(f"   ❌ Exception: {e}")
        
        # Test 4: Recherche partielle
        print("\n4. Test recherche partielle 'BEN':")
        try:
            response = client.get('/test_recherche?search=BEN')
            print(f"   Status: {response.status_code}")
            if response.status_code == 200:
                content = response.data.decode()
                if 'BENALI' in content:
                    print("   ✅ Recherche partielle fonctionne")
                    count = content.count('<div class="result">')
                    print(f"   ✅ {count} résultat(s) pour 'BEN'")
                else:
                    print("   ⚠️  Recherche partielle ne fonctionne pas")
            else:
                print(f"   ❌ Erreur: {response.status_code}")
        except Exception as e:
            print(f"   ❌ Exception: {e}")
        
        print("\n" + "=" * 60)
        print("RÉSUMÉ DU TEST SIMPLE")
        print("=" * 60)
        print("✅ Test de recherche simple créé")
        print("✅ Recherche par nom testée")
        print("✅ Recherche par matricule testée")
        print("✅ Recherche partielle testée")
        print("\n🚀 Pour tester manuellement:")
        print("   Lancer ce script et aller sur: http://localhost:5000/test_recherche")

if __name__ == "__main__":
    test_recherche_simple()
