#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Test pour vérifier que la pagination améliorée fonctionne
"""

import sys
import os
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from app import app
from rh_models import Personnel
from db import db

def test_pagination_amelioree():
    """Test de la pagination avec 100 éléments par page"""
    
    with app.app_context():
        try:
            # Compter le total de personnel
            total_personnel = Personnel.query.count()
            print(f"📊 Total personnel dans la base : {total_personnel}")
            
            if total_personnel == 0:
                print("❌ Aucun personnel trouvé dans la base de données")
                return False
            
            # Test de pagination - Page 1 (100 premiers)
            per_page = 100
            page1 = Personnel.query.join(Personnel.arme)\
                                  .join(Personnel.unite)\
                                  .join(Personnel.grade_actuel)\
                                  .join(Personnel.categorie)\
                                  .order_by(Personnel.nom, Personnel.prenom)\
                                  .paginate(page=1, per_page=per_page, error_out=False)
            
            print(f"✅ Page 1 : {len(page1.items)} éléments")
            print(f"📄 Total pages : {page1.pages}")
            print(f"📊 Total éléments : {page1.total}")
            print(f"🔢 Éléments par page : {page1.per_page}")
            
            # Test de pagination - Page 2 (100 suivants)
            if page1.pages > 1:
                page2 = Personnel.query.join(Personnel.arme)\
                                      .join(Personnel.unite)\
                                      .join(Personnel.grade_actuel)\
                                      .join(Personnel.categorie)\
                                      .order_by(Personnel.nom, Personnel.prenom)\
                                      .paginate(page=2, per_page=per_page, error_out=False)
                
                print(f"✅ Page 2 : {len(page2.items)} éléments")
                
                # Vérifier que les éléments sont différents
                matricules_page1 = {p.matricule for p in page1.items}
                matricules_page2 = {p.matricule for p in page2.items}
                
                if matricules_page1.intersection(matricules_page2):
                    print("⚠️ ATTENTION : Doublons détectés entre les pages")
                else:
                    print("✅ Aucun doublon entre les pages")
            
            # Afficher quelques exemples de la première page
            print(f"\n📋 Premiers 5 résultats de la page 1 :")
            for i, p in enumerate(page1.items[:5]):
                print(f"  {i+1}. {p.matricule} - {p.nom} {p.prenom}")
            
            # Vérifications
            expected_pages = (total_personnel + per_page - 1) // per_page  # Division avec arrondi vers le haut
            
            if page1.pages == expected_pages:
                print(f"✅ Nombre de pages correct : {page1.pages}")
            else:
                print(f"⚠️ Nombre de pages incorrect : {page1.pages} (attendu: {expected_pages})")
            
            if page1.total == total_personnel:
                print(f"✅ Total correct : {page1.total}")
            else:
                print(f"⚠️ Total incorrect : {page1.total} (attendu: {total_personnel})")
            
            return True
            
        except Exception as e:
            print(f"❌ Erreur lors du test : {str(e)}")
            import traceback
            traceback.print_exc()
            return False

def test_recherche_avec_pagination():
    """Test de recherche avec pagination"""
    
    with app.app_context():
        try:
            # Test de recherche par nom avec pagination
            search_term = "ALAMI"
            per_page = 100
            
            resultats_paginated = Personnel.query.filter(
                Personnel.nom.ilike(f'%{search_term}%')
            ).order_by(Personnel.nom, Personnel.prenom)\
             .paginate(page=1, per_page=per_page, error_out=False)
            
            print(f"\n🔍 Recherche '{search_term}' avec pagination :")
            print(f"✅ Résultats trouvés : {resultats_paginated.total}")
            print(f"📄 Pages nécessaires : {resultats_paginated.pages}")
            print(f"📋 Résultats sur page 1 : {len(resultats_paginated.items)}")
            
            # Afficher quelques exemples
            for i, p in enumerate(resultats_paginated.items[:3]):
                print(f"  {i+1}. {p.matricule}: {p.nom} {p.prenom}")
            
            return True
            
        except Exception as e:
            print(f"❌ Erreur lors de la recherche : {str(e)}")
            return False

def test_navigation_pages():
    """Test de navigation entre les pages"""
    
    with app.app_context():
        try:
            per_page = 100
            total_personnel = Personnel.query.count()
            
            if total_personnel <= per_page:
                print(f"\n📄 Navigation : Une seule page nécessaire ({total_personnel} éléments)")
                return True
            
            # Test page 1
            page1 = Personnel.query.order_by(Personnel.nom, Personnel.prenom)\
                                  .paginate(page=1, per_page=per_page, error_out=False)
            
            print(f"\n📄 Navigation entre les pages :")
            print(f"Page 1 - has_prev: {page1.has_prev}, has_next: {page1.has_next}")
            
            if page1.has_next:
                # Test page 2
                page2 = Personnel.query.order_by(Personnel.nom, Personnel.prenom)\
                                      .paginate(page=2, per_page=per_page, error_out=False)
                
                print(f"Page 2 - has_prev: {page2.has_prev}, has_next: {page2.has_next}")
                
                # Test dernière page
                last_page = Personnel.query.order_by(Personnel.nom, Personnel.prenom)\
                                          .paginate(page=page1.pages, per_page=per_page, error_out=False)
                
                print(f"Page {page1.pages} (dernière) - has_prev: {last_page.has_prev}, has_next: {last_page.has_next}")
            
            return True
            
        except Exception as e:
            print(f"❌ Erreur lors du test de navigation : {str(e)}")
            return False

if __name__ == "__main__":
    print("🧪 Test de pagination améliorée (100 éléments par page)")
    print("=" * 60)
    
    success1 = test_pagination_amelioree()
    success2 = test_recherche_avec_pagination()
    success3 = test_navigation_pages()
    
    print("\n" + "=" * 60)
    if success1 and success2 and success3:
        print("🎉 TOUS LES TESTS RÉUSSIS")
        print("✅ La pagination améliorée fonctionne correctement")
        print("📄 100 éléments par page avec navigation fluide")
    else:
        print("❌ CERTAINS TESTS ONT ÉCHOUÉ")
