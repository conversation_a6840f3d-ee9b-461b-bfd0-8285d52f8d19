# Corrections apportées au formulaire d'ajout de militaire

## Problèmes identifiés et corrigés

### 1. État Matrimonial
**Problème :** Le template utilisait `etat.id_etat` mais le modèle utilise `id_sitfam`
**Correction :** Changé `{{ etat.id_etat }}` en `{{ etat.id_sitfam }}` dans le template

### 2. Service/Arme  
**Problème :** Le template utilisait `service.id_service` mais le modèle utilise `id_arme`
**Correction :** Changé `{{ service.id_service }}` en `{{ service.id_arme }}` dans le template

### 3. Lien de Parenté
**Problème :** Le template utilisait `lien.id_lien` mais le modèle utilise `id_degre`
**Correction :** Chang<PERSON> `{{ lien.id_lien }}` en `{{ lien.id_degre }}` dans le template

### 4. Spécialités dynamiques
**Problème :** Les spécialités étaient codées en dur au lieu d'être chargées dynamiquement
**Correction :** Implémenté un appel AJAX vers `/rh/api/specialites/{service_id}` pour charger les spécialités selon le service sélectionné

### 5. Données de référence manquantes
**Problème :** Les tables de référence ne contenaient pas toutes les données selon architecture_rh.md
**Correction :** Créé et exécuté `insert_reference_data.py` pour ajouter les données manquantes

## Données de référence ajoutées

### États matrimoniaux (selon architecture_rh.md)
- Célibataire
- Marié(e)
- Divorcé(e)
- Veuf / Veuve

### Services/Armes (selon architecture_rh.md)
- Artillerie
- Blindé
- Infanterie
- Transmission
- Intendance
- Cavalerie
- Santé
- Matériel
- Génie

### Spécialités (selon architecture_rh.md)
- sol-sol (Artillerie)
- sol-air (Artillerie)

## Fichiers modifiés

1. **templates/rh/nouveau_militaire.html**
   - Ligne 135: Corrigé `service.id_service` → `service.id_arme`
   - Ligne 299: Corrigé `etat.id_etat` → `etat.id_sitfam`
   - Ligne 251: Corrigé `lien.id_lien` → `lien.id_degre`
   - Lignes 378-406: Remplacé le code JavaScript statique par un appel AJAX dynamique

2. **insert_reference_data.py** (nouveau fichier)
   - Script pour insérer les données de référence manquantes
   - Respecte les contraintes de clés étrangères existantes

3. **test_reference_data.py** (nouveau fichier)
   - Script de test pour vérifier les données de référence

## Vérification des corrections

### Champs template corrigés ✅
- État matrimonial: `id_sitfam` ✓
- Service/Arme: `id_arme` ✓  
- Spécialité: `id_specialite` ✓
- Degré parenté: `id_degre` ✓

### API spécialités ✅
- Route: `/rh/api/specialites/<int:service_id>`
- Retourne les spécialités filtrées par service
- Format JSON: `[{"id": 1, "libelle": "sol-sol"}, ...]`

### Données de référence ✅
- 5 états matrimoniaux
- 9 services/armes
- 2 spécialités (Artillerie)
- 8 degrés de parenté
- Autres données complètes (genres, catégories, etc.)

## Résultat

Le formulaire d'ajout de militaire affiche maintenant correctement :
- ✅ Les suggestions d'état matrimonial depuis la table de référence
- ✅ Les suggestions de service depuis la table de référence  
- ✅ Les spécialités chargées dynamiquement selon le service sélectionné
- ✅ Tous les champs sont optionnels (non obligatoires) comme demandé

Les données respectent fidèlement les spécifications de `architecture_rh.md`.
