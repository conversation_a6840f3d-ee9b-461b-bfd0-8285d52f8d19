#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Debug détaillé de la recherche RH
"""

from flask import Flask
from db import db, init_app
from rh_models import *
from rh import rh_bp
from sqlalchemy import or_

def create_debug_app():
    """Créer l'application Flask pour debug"""
    app = Flask(__name__)
    app.secret_key = 'test_key'
    app.config['DEBUG'] = True
    init_app(app)
    app.register_blueprint(rh_bp)
    
    @app.route('/')
    def index():
        return "Debug RH - <a href='/rh/recherche'>Recherche Personnel</a>"
    
    return app

def test_recherche_directe():
    """Test direct de la logique de recherche"""
    app = create_debug_app()
    
    with app.app_context():
        print("=" * 60)
        print("TEST DIRECT DE LA LOGIQUE DE RECHERCHE")
        print("=" * 60)
        
        try:
            # Test 1: Recherche simple par nom
            print("\n1. Test recherche par nom 'OUALI':")
            query = Personnel.query.filter(
                or_(
                    Personnel.nom.ilike('%OUALI%'),
                    Personnel.prenom.ilike('%OUALI%'),
                    Personnel.nom_arabe.ilike('%OUALI%'),
                    Personnel.prenom_arabe.ilike('%OUALI%')
                )
            )
            resultats = query.all()
            print(f"   Résultats trouvés: {len(resultats)}")
            for r in resultats:
                print(f"   - {r.matricule}: {r.nom} {r.prenom}")
            
            # Test 2: Recherche avec jointures
            print("\n2. Test recherche avec jointures:")
            try:
                query_avec_jointures = Personnel.query.filter(
                    Personnel.nom.ilike('%OUALI%')
                ).join(Personnel.arme).join(Personnel.unite).join(Personnel.grade_actuel)
                
                resultats_jointures = query_avec_jointures.all()
                print(f"   Résultats avec jointures: {len(resultats_jointures)}")
                for r in resultats_jointures:
                    print(f"   - {r.matricule}: {r.nom} {r.prenom}")
                    print(f"     Arme: {r.arme.libelle if r.arme else 'N/A'}")
                    print(f"     Unité: {r.unite.libelle if r.unite else 'N/A'}")
                    print(f"     Grade: {r.grade_actuel.libelle if r.grade_actuel else 'N/A'}")
                    
            except Exception as e:
                print(f"   ❌ Erreur avec jointures: {e}")
                
                # Test sans jointures
                print("\n   Test sans jointures:")
                resultats_sans_jointures = Personnel.query.filter(
                    Personnel.nom.ilike('%OUALI%')
                ).all()
                print(f"   Résultats sans jointures: {len(resultats_sans_jointures)}")
                
                # Vérifier les relations
                for r in resultats_sans_jointures:
                    print(f"   - {r.matricule}: {r.nom} {r.prenom}")
                    try:
                        print(f"     Arme ID: {r.arme_id}, Arme: {r.arme.libelle if r.arme else 'NULL'}")
                    except Exception as arme_err:
                        print(f"     ❌ Erreur arme: {arme_err}")
                    try:
                        print(f"     Unité ID: {r.unite_id}, Unité: {r.unite.libelle if r.unite else 'NULL'}")
                    except Exception as unite_err:
                        print(f"     ❌ Erreur unité: {unite_err}")
                    try:
                        print(f"     Grade ID: {r.grade_actuel_id}, Grade: {r.grade_actuel.libelle if r.grade_actuel else 'NULL'}")
                    except Exception as grade_err:
                        print(f"     ❌ Erreur grade: {grade_err}")
            
        except Exception as e:
            print(f"❌ Erreur générale: {e}")

def test_template_recherche():
    """Test du template de recherche"""
    app = create_debug_app()
    
    with app.test_client() as client:
        with app.app_context():
            print("\n" + "=" * 60)
            print("TEST DU TEMPLATE DE RECHERCHE")
            print("=" * 60)
            
            # Test avec capture des erreurs
            print("\n1. Test page de recherche avec debug:")
            try:
                response = client.get('/rh/recherche')
                print(f"   Status: {response.status_code}")
                if response.status_code == 200:
                    print("   ✅ Page accessible")
                else:
                    print(f"   ❌ Erreur: {response.data.decode()[:500]}")
            except Exception as e:
                print(f"   ❌ Exception: {e}")
            
            # Test recherche avec debug
            print("\n2. Test recherche 'OUALI' avec debug:")
            try:
                response = client.get('/rh/recherche?search=OUALI')
                print(f"   Status: {response.status_code}")
                
                if response.status_code == 200:
                    content = response.data.decode()
                    if 'OUALI' in content:
                        print("   ✅ Résultats affichés")
                    else:
                        print("   ⚠️  Pas de résultats visibles")
                        print(f"   Contenu (premiers 500 chars): {content[:500]}")
                elif response.status_code == 302:
                    print("   ⚠️  Redirection détectée")
                    location = response.headers.get('Location', 'Non spécifiée')
                    print(f"   Redirection vers: {location}")
                else:
                    print(f"   ❌ Erreur: {response.data.decode()[:500]}")
                    
            except Exception as e:
                print(f"   ❌ Exception: {e}")

def verifier_referentiels():
    """Vérifier l'état des référentiels"""
    app = create_debug_app()
    
    with app.app_context():
        print("\n" + "=" * 60)
        print("VÉRIFICATION DES RÉFÉRENTIELS")
        print("=" * 60)
        
        referentiels = [
            ('Armes', ReferentielArme),
            ('Unités', ReferentielUnite),
            ('Grades', ReferentielGrade),
            ('Catégories', ReferentielCategorie)
        ]
        
        for nom, model in referentiels:
            try:
                count = model.query.count()
                print(f"{nom}: {count} enregistrement(s)")
                if count > 0:
                    exemples = model.query.limit(3).all()
                    for ex in exemples:
                        print(f"  - {ex.libelle}")
            except Exception as e:
                print(f"❌ Erreur {nom}: {e}")

if __name__ == "__main__":
    verifier_referentiels()
    test_recherche_directe()
    test_template_recherche()
