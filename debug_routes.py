#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Debug des routes disponibles
"""

from flask import Flask
from db import db, init_app
from rh_models import *
from rh_blueprint import rh_bp

def debug_routes():
    """Debug des routes disponibles"""
    app = Flask(__name__)
    app.secret_key = 'debug_key'
    init_app(app)
    app.register_blueprint(rh_bp)
    
    with app.app_context():
        print("=" * 60)
        print("DEBUG DES ROUTES DISPONIBLES")
        print("=" * 60)
        
        print("\nRoutes enregistrées dans l'application:")
        for rule in app.url_map.iter_rules():
            print(f"  {rule.rule} -> {rule.endpoint} [{', '.join(rule.methods)}]")
        
        print(f"\nBlueprint RH enregistré: {rh_bp.name}")
        print(f"URL prefix: {rh_bp.url_prefix}")
        
        # Test direct des routes
        with app.test_client() as client:
            print("\nTest des routes RH:")
            
            routes_to_test = [
                '/rh/',
                '/rh/recherche',
                '/rh/dashboard'
            ]
            
            for route in routes_to_test:
                try:
                    response = client.get(route)
                    print(f"  {route}: Status {response.status_code}")
                except Exception as e:
                    print(f"  {route}: Erreur {e}")

if __name__ == "__main__":
    debug_routes()
