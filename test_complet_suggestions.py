#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Test complet des suggestions dans les formulaires
"""

import requests
import json

def test_complet_suggestions():
    """Test complet des suggestions dans tous les formulaires"""
    base_url = "http://localhost:3000"
    
    print("🔍 Test complet des suggestions dans les formulaires RH...")
    
    try:
        # Test 1: Formulaire d'ajout de militaire
        print("\n1. Test du formulaire d'ajout de militaire:")
        response = requests.get(f"{base_url}/rh/nouveau_militaire")
        
        if response.status_code == 200:
            print("✅ Formulaire d'ajout accessible")
            
            html_content = response.text
            
            # Vérifier les suggestions dans le formulaire d'ajout
            checks_ajout = [
                ('État matrimonial', 'Célibataire'),
                ('Service/Arme', 'Artillerie'),
                ('Spécialité (dynamique)', 'service_select'),
                ('Lien de parenté', 'name="degre_parente_id"')
            ]
            
            for nom, pattern in checks_ajout:
                if pattern in html_content:
                    print(f"  ✅ {nom}: Présent")
                else:
                    print(f"  ❌ {nom}: Manquant")
        else:
            print(f"❌ Erreur d'accès au formulaire d'ajout: {response.status_code}")
        
        # Test 2: Interface de recherche
        print("\n2. Test de l'interface de recherche:")
        response = requests.get(f"{base_url}/rh/recherche")
        
        if response.status_code == 200:
            print("✅ Interface de recherche accessible")
            
            html_content = response.text
            
            # Vérifier les suggestions dans l'interface de recherche
            checks_recherche = [
                ('Service/Arme', 'Artillerie'),
                ('État matrimonial', 'Célibataire'),
                ('Grade', 'name="grade_id"'),
                ('Unité', 'name="unite_id"'),
                ('Genre', 'name="genre_id"')
            ]
            
            for nom, pattern in checks_recherche:
                if pattern in html_content:
                    print(f"  ✅ {nom}: Présent")
                else:
                    print(f"  ❌ {nom}: Manquant")
        else:
            print(f"❌ Erreur d'accès à l'interface de recherche: {response.status_code}")
        
        # Test 3: API des spécialités
        print("\n3. Test de l'API des spécialités:")
        response = requests.get(f"{base_url}/rh/api/specialites/1")
        
        if response.status_code == 200:
            data = response.json()
            if data.get('success') and data.get('specialites'):
                specialites = data['specialites']
                print(f"✅ API spécialités fonctionne: {len(specialites)} spécialité(s)")
                for spec in specialites:
                    print(f"    - {spec['libelle']} (ID: {spec['id']})")
            else:
                print("❌ API spécialités: Pas de données")
        else:
            print(f"❌ Erreur API spécialités: {response.status_code}")
        
        # Test 4: Test de recherche avec filtres
        print("\n4. Test de recherche avec filtres:")
        
        params = {
            'search': 'OUALI',
            'service_id': '1',
            'etat_matrimonial_id': '1'
        }
        
        response = requests.get(f"{base_url}/rh/recherche", params=params)
        
        if response.status_code == 200:
            print("✅ Recherche avec filtres multiples fonctionne")
        else:
            print(f"❌ Erreur recherche avec filtres: {response.status_code}")
        
        print("\n✅ Test complet terminé!")
        
        print("\n🎯 Résumé des corrections effectuées:")
        print("\n📝 FORMULAIRE D'AJOUT (/rh/nouveau_militaire):")
        print("  ✅ État matrimonial: etat.id_etat → etat.id_sitfam")
        print("  ✅ Service/Arme: service.id_service → service.id_arme")
        print("  ✅ Lien de parenté: lien.id_lien → lien.id_degre")
        print("  ✅ Variables: armes → services, situations_familiales → etats_matrimoniaux")
        print("  ✅ Spécialités: Chargement dynamique via API")
        print("  ✅ Template: RH/nouveau_militaire.html → rh/nouveau_militaire.html")
        
        print("\n🔍 INTERFACE DE RECHERCHE (/rh/recherche):")
        print("  ✅ Service/Arme: service.id_service → service.id_arme")
        print("  ✅ État matrimonial: etat.id_etat → etat.id_sitfam")
        print("  ✅ Variables: armes → services, ajout genres et etats_matrimoniaux")
        print("  ✅ Template: rh/recherche_personnel.html → RH/recherche_personnel.html")
        
        print("\n📊 DONNÉES DE RÉFÉRENCE:")
        print("  ✅ États matrimoniaux: Célibataire, Marié(e), Divorcé(e), Veuf/Veuve")
        print("  ✅ Services/Armes: Artillerie, Blindé, Infanterie, etc.")
        print("  ✅ Spécialités: sol-sol, sol-air pour Artillerie")
        
        print("\n🔧 CORRECTIONS TECHNIQUES:")
        print("  ✅ Noms de champs alignés avec les modèles SQLAlchemy")
        print("  ✅ Variables de template cohérentes entre routes et templates")
        print("  ✅ API spécialités avec structure de réponse correcte")
        print("  ✅ JavaScript corrigé pour utiliser data.specialites")
        
        return True
        
    except requests.exceptions.ConnectionError:
        print("❌ Impossible de se connecter au serveur. Assurez-vous que l'application Flask est en cours d'exécution sur le port 3000.")
        return False
    except Exception as e:
        print(f"❌ Erreur lors du test: {str(e)}")
        return False

if __name__ == "__main__":
    success = test_complet_suggestions()
    if success:
        print("\n🎉 SUCCÈS COMPLET!")
        print("Toutes les suggestions fonctionnent maintenant correctement dans:")
        print("- Le formulaire d'ajout de militaire")
        print("- L'interface de recherche")
        print("- Les API de données de référence")
        print("\nLes données de référence sont fidèles aux spécifications de architecture_rh.md")
    else:
        print("\n❌ Certains tests ont échoué.")
