#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Script pour insérer les données de référence selon architecture_rh.md
"""

import sys
import os
from flask import Flask
from db import db
from rh_models import *

def create_app():
    """Créer l'application Flask pour le contexte de base de données"""
    app = Flask(__name__)
    app.config['SQLALCHEMY_DATABASE_URI'] = 'mysql+pymysql://root:@localhost/gestion_vehicules'
    app.config['SQLALCHEMY_TRACK_MODIFICATIONS'] = False
    db.init_app(app)
    return app

def insert_reference_data():
    """Insérer les données de référence selon architecture_rh.md"""
    app = create_app()
    
    with app.app_context():
        try:
            print("🚀 Insertion des données de référence RH...")
            
            # 1. États matrimoniaux selon architecture_rh.md
            print("📝 Insertion des états matrimoniaux...")
            etats_matrimoniaux = [
                "Célibataire",
                "Marié(e)",
                "Divorcé(e)",
                "Veuf / Veuve"
            ]

            # Ajouter seulement les états manquants
            for etat in etats_matrimoniaux:
                existing = ReferentielSituationFamiliale.query.filter_by(libelle=etat).first()
                if not existing:
                    nouveau_etat = ReferentielSituationFamiliale(libelle=etat)
                    db.session.add(nouveau_etat)
                    print(f"  + Ajouté: {etat}")
            
            # 2. Services/Armes selon architecture_rh.md
            print("📝 Insertion des services/armes...")
            services = [
                "Artillerie",
                "Blindé",
                "Infanterie",
                "Transmission",
                "Intendance",
                "Cavalerie",
                "Santé",
                "Matériel",
                "Génie"
            ]

            # Ajouter seulement les services manquants
            for service in services:
                existing = ReferentielArme.query.filter_by(libelle=service).first()
                if not existing:
                    nouveau_service = ReferentielArme(libelle=service)
                    db.session.add(nouveau_service)
                    print(f"  + Ajouté: {service}")
            
            # Commit pour obtenir les IDs
            db.session.commit()
            
            # 3. Spécialités selon architecture_rh.md
            print("📝 Insertion des spécialités...")

            # Récupérer les services pour les spécialités
            artillerie = ReferentielArme.query.filter_by(libelle="Artillerie").first()

            if artillerie:
                specialites = [
                    {"libelle": "sol-sol", "id_arme": artillerie.id_arme},
                    {"libelle": "sol-air", "id_arme": artillerie.id_arme}
                ]

                for spec in specialites:
                    existing = ReferentielSpecialite.query.filter_by(
                        libelle=spec["libelle"],
                        id_arme=spec["id_arme"]
                    ).first()
                    if not existing:
                        nouvelle_spec = ReferentielSpecialite(
                            libelle=spec["libelle"],
                            id_arme=spec["id_arme"]
                        )
                        db.session.add(nouvelle_spec)
                        print(f"  + Ajouté: {spec['libelle']} (Artillerie)")
            
            # 4. Autres données de référence si manquantes
            print("📝 Vérification des autres données de référence...")
            
            # Genres
            if ReferentielGenre.query.count() == 0:
                genres = ["Masculin", "Féminin"]
                for genre in genres:
                    nouveau_genre = ReferentielGenre(libelle=genre)
                    db.session.add(nouveau_genre)
            
            # Groupes sanguins
            if ReferentielGroupeSanguin.query.count() == 0:
                groupes = ["A+", "A−", "B+", "B−", "AB+", "AB−", "O+", "O−"]
                for groupe in groupes:
                    nouveau_groupe = ReferentielGroupeSanguin(libelle=groupe)
                    db.session.add(nouveau_groupe)
            
            # Catégories
            if ReferentielCategorie.query.count() == 0:
                categories = ["Officier", "Officier du rang", "Militaire du rang"]
                for categorie in categories:
                    nouvelle_categorie = ReferentielCategorie(libelle=categorie)
                    db.session.add(nouvelle_categorie)
            
            # Grades
            if ReferentielGrade.query.count() == 0:
                grades = [
                    "soldat 1°classe", "soldat 2° classe", "brigadier", "brigadier chef",
                    "MDL", "MDL Chef", "adjudant", "adjudant chef", "sous-lieutenant",
                    "lieutenant", "capitaine", "commandant", "lt-colonel", "colonel"
                ]
                for grade in grades:
                    nouveau_grade = ReferentielGrade(libelle=grade)
                    db.session.add(nouveau_grade)
            
            # Unités (exemples)
            if ReferentielUnite.query.count() == 0:
                unites = [
                    "1er Régiment d'Artillerie", "2ème Régiment d'Infanterie",
                    "3ème Régiment Blindé", "4ème Régiment de Transmission",
                    "5ème Régiment du Génie", "6ème Régiment de Cavalerie",
                    "7ème Régiment d'Intendance", "8ème Régiment de Santé",
                    "9ème Régiment de Matériel", "10ème Régiment Mixte"
                ]
                for unite in unites:
                    nouvelle_unite = ReferentielUnite(libelle=unite)
                    db.session.add(nouvelle_unite)
            
            # Degrés de parenté
            if ReferentielDegreParente.query.count() == 0:
                degres = ["Père", "Mère", "Frère", "Sœur", "Époux/Épouse", "Fils", "Fille", "Autre"]
                for degre in degres:
                    nouveau_degre = ReferentielDegreParente(libelle=degre)
                    db.session.add(nouveau_degre)
            
            # Commit final
            db.session.commit()
            
            print("✅ Données de référence insérées avec succès!")
            
            # Vérification
            print("\n📊 Vérification des données insérées:")
            print(f"- États matrimoniaux: {ReferentielSituationFamiliale.query.count()}")
            print(f"- Services/Armes: {ReferentielArme.query.count()}")
            print(f"- Spécialités: {ReferentielSpecialite.query.count()}")
            print(f"- Genres: {ReferentielGenre.query.count()}")
            print(f"- Groupes sanguins: {ReferentielGroupeSanguin.query.count()}")
            print(f"- Catégories: {ReferentielCategorie.query.count()}")
            print(f"- Grades: {ReferentielGrade.query.count()}")
            print(f"- Unités: {ReferentielUnite.query.count()}")
            print(f"- Degrés de parenté: {ReferentielDegreParente.query.count()}")
            
        except Exception as e:
            db.session.rollback()
            print(f"❌ Erreur lors de l'insertion: {str(e)}")
            return False
        
        return True

if __name__ == "__main__":
    success = insert_reference_data()
    sys.exit(0 if success else 1)
