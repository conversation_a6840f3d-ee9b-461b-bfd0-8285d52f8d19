#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Test final de l'affichage des champs CIN et Unité
"""

import sys
import os
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from app import app
from rh_models import Personnel
from db import db

def test_affichage_complet():
    """Test complet de l'affichage avec pagination"""
    
    with app.app_context():
        try:
            print("🧪 Test final de l'affichage avec pagination")
            print("=" * 60)
            
            # Test de la requête exacte utilisée dans rh_blueprint.py
            page = 1
            per_page = 100
            
            personnel_paginated = Personnel.query.join(Personnel.arme)\
                                                .join(Personnel.unite)\
                                                .join(Personnel.grade_actuel)\
                                                .join(Personnel.categorie)\
                                                .order_by(Personnel.nom, Personnel.prenom)\
                                                .paginate(page=page, per_page=per_page, error_out=False)
            
            print(f"📊 Résultats paginés:")
            print(f"   - Total: {personnel_paginated.total}")
            print(f"   - Pages: {personnel_paginated.pages}")
            print(f"   - Page actuelle: {personnel_paginated.page}")
            print(f"   - Éléments sur cette page: {len(personnel_paginated.items)}")
            
            print(f"\n👥 Premiers 10 militaires avec tous les champs:")
            print("-" * 80)
            
            for i, militaire in enumerate(personnel_paginated.items[:10], 1):
                print(f"{i:2d}. {militaire.matricule} | {militaire.nom} {militaire.prenom}")
                print(f"    🆔 CIN: {militaire.numero_cin}")
                print(f"    📅 Expiration CIN: {militaire.date_expiration_cin}")
                print(f"    🏢 Unité: {militaire.unite.libelle}")
                print(f"    ⚔️ Arme: {militaire.arme.libelle}")
                print(f"    🎖️ Grade: {militaire.grade_actuel.libelle}")
                print(f"    📋 Fonction: {militaire.fonction}")
                print()
            
            return True
            
        except Exception as e:
            print(f"❌ Erreur lors du test : {str(e)}")
            import traceback
            traceback.print_exc()
            return False

def test_recherche_avec_affichage():
    """Test de recherche avec affichage des champs"""
    
    with app.app_context():
        try:
            print("🔍 Test de recherche avec affichage complet")
            print("=" * 60)
            
            # Test de recherche par nom
            search_term = "ALAMI"
            page = 1
            per_page = 100
            
            query = Personnel.query.filter(
                Personnel.nom.ilike(f'%{search_term}%')
            )
            
            resultats_paginated = query.order_by(Personnel.nom, Personnel.prenom)\
                                     .paginate(page=page, per_page=per_page, error_out=False)
            
            print(f"🔍 Recherche '{search_term}':")
            print(f"   - Résultats trouvés: {resultats_paginated.total}")
            print(f"   - Pages: {resultats_paginated.pages}")
            print(f"   - Résultats sur page 1: {len(resultats_paginated.items)}")
            
            print(f"\n📋 Résultats de recherche avec tous les champs:")
            print("-" * 80)
            
            for i, militaire in enumerate(resultats_paginated.items, 1):
                print(f"{i}. {militaire.matricule} | {militaire.nom} {militaire.prenom}")
                print(f"   🆔 CIN: {militaire.numero_cin}")
                print(f"   🏢 Unité: {militaire.unite.libelle}")
                print(f"   ⚔️ Arme: {militaire.arme.libelle}")
                print(f"   🎖️ Grade: {militaire.grade_actuel.libelle}")
                print()
            
            return True
            
        except Exception as e:
            print(f"❌ Erreur lors de la recherche : {str(e)}")
            import traceback
            traceback.print_exc()
            return False

def test_template_structure():
    """Test de la structure du template corrigé"""
    
    try:
        print("📄 Test de la structure du template")
        print("=" * 60)
        
        template_path = "templates/rh/recherche_personnel.html"
        
        with open(template_path, 'r', encoding='utf-8') as f:
            contenu = f.read()
        
        # Vérifications importantes
        verifications = [
            ('numero_cin', 'Affichage du numéro de CIN'),
            ('unite.libelle', 'Affichage du libellé de l\'unité'),
            ('arme.libelle', 'Affichage du libellé de l\'arme'),
            ('date_expiration_cin', 'Référence correcte à la date d\'expiration'),
            ('<th>Arme</th>', 'En-tête "Arme" correct'),
            ('<th>Unité</th>', 'En-tête "Unité" correct'),
            ('<th>CIN</th>', 'En-tête "CIN" correct'),
            ('d-flex flex-column', 'Structure d\'affichage CIN'),
            ('fw-bold', 'Style pour le numéro de CIN'),
            ('badge bg-danger', 'Badge pour CIN expirée'),
            ('badge bg-warning', 'Badge pour CIN bientôt expirée'),
            ('badge bg-success', 'Badge pour CIN valide')
        ]
        
        print("🔍 Vérifications du template:")
        all_good = True
        
        for texte, description in verifications:
            if texte in contenu:
                print(f"   ✅ {description}")
            else:
                print(f"   ❌ {description}")
                all_good = False
        
        return all_good
        
    except Exception as e:
        print(f"❌ Erreur lors du test template : {str(e)}")
        return False

if __name__ == "__main__":
    print("🧪 Test final de l'affichage CIN et Unité")
    print("=" * 60)
    
    success1 = test_affichage_complet()
    success2 = test_recherche_avec_affichage()
    success3 = test_template_structure()
    
    print("\n" + "=" * 60)
    if success1 and success2 and success3:
        print("🎉 TOUS LES TESTS RÉUSSIS")
        print("✅ L'affichage des champs CIN et Unité fonctionne parfaitement")
        print("📋 Fonctionnalités validées:")
        print("   - Numéro de CIN affiché")
        print("   - Libellé complet de l'unité affiché")
        print("   - Libellé de l'arme affiché (au lieu de service)")
        print("   - Statut d'expiration de la CIN avec badges colorés")
        print("   - Pagination avec 100 éléments par page")
        print("   - Recherche avec affichage complet")
    else:
        print("❌ CERTAINS TESTS ONT ÉCHOUÉ")
        print("⚠️ Vérifiez les détails ci-dessus")
