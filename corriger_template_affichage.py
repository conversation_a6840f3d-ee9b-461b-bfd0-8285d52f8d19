#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Script pour corriger l'affichage des champs CIN et Unité dans le template
"""

import os

def corriger_template():
    """Corriger le template de recherche personnel"""
    
    template_path = "templates/rh/recherche_personnel.html"
    
    if not os.path.exists(template_path):
        print(f"❌ Template non trouvé : {template_path}")
        return False
    
    try:
        # Lire le contenu actuel
        with open(template_path, 'r', encoding='utf-8') as f:
            contenu = f.read()
        
        print("🔧 Correction du template de recherche personnel...")
        
        # Corrections à appliquer
        corrections = [
            # 1. Corriger l'en-tête "Service" -> "Arme"
            ('<th>Service</th>', '<th>Arme</th>'),
            
            # 2. Corriger l'affichage de l'unité (utiliser libelle au lieu de code)
            ('{{ militaire.unite.code if militaire.unite else \'N/A\' }}', 
             '{{ militaire.unite.libelle if militaire.unite else \'N/A\' }}'),
            
            # 3. Corriger l'affichage du service -> arme
            ('{{ militaire.service.code_court if militaire.service else \'N/A\' }}',
             '{{ militaire.arme.libelle if militaire.arme else \'N/A\' }}'),
            
            # 4. Corriger l'affichage de la CIN - ajouter le numéro
            ('{% if militaire.cin_date_expiration %}',
             '''<div class="d-flex flex-column">
                                            <small class="fw-bold">{{ militaire.numero_cin if militaire.numero_cin else 'N/A' }}</small>
                                        {% if militaire.date_expiration_cin %}'''),
            
            # 5. Corriger la référence à cin_date_expiration -> date_expiration_cin
            ('{% set jours_restants = (militaire.cin_date_expiration - date.today()).days %}',
             '{% set jours_restants = (militaire.date_expiration_cin - date.today()).days %}'),
            
            # 6. Fermer la div ajoutée
            ('{% endif %}\n                                    </td>',
             '''{% endif %}
                                        </div>
                                    </td>''')
        ]
        
        # Appliquer les corrections
        contenu_corrige = contenu
        corrections_appliquees = 0
        
        for ancien, nouveau in corrections:
            if ancien in contenu_corrige:
                contenu_corrige = contenu_corrige.replace(ancien, nouveau)
                corrections_appliquees += 1
                print(f"   ✅ Correction appliquée: {ancien[:50]}...")
            else:
                print(f"   ⚠️ Texte non trouvé: {ancien[:50]}...")
        
        # Sauvegarder le fichier corrigé
        with open(template_path, 'w', encoding='utf-8') as f:
            f.write(contenu_corrige)
        
        print(f"\n🎉 Template corrigé avec succès!")
        print(f"📊 {corrections_appliquees}/{len(corrections)} corrections appliquées")
        
        return True
        
    except Exception as e:
        print(f"❌ Erreur lors de la correction : {str(e)}")
        return False

def verifier_corrections():
    """Vérifier que les corrections ont été appliquées"""
    
    template_path = "templates/rh/recherche_personnel.html"
    
    try:
        with open(template_path, 'r', encoding='utf-8') as f:
            contenu = f.read()
        
        print("\n🔍 Vérification des corrections...")
        
        # Vérifications
        verifications = [
            ('numero_cin', 'Affichage du numéro de CIN'),
            ('unite.libelle', 'Affichage du libellé de l\'unité'),
            ('arme.libelle', 'Affichage du libellé de l\'arme'),
            ('date_expiration_cin', 'Référence correcte à la date d\'expiration CIN'),
            ('<th>Arme</th>', 'En-tête "Arme" au lieu de "Service"')
        ]
        
        for texte, description in verifications:
            if texte in contenu:
                print(f"   ✅ {description}")
            else:
                print(f"   ❌ {description}")
        
        return True
        
    except Exception as e:
        print(f"❌ Erreur lors de la vérification : {str(e)}")
        return False

if __name__ == "__main__":
    print("🔧 Correction du template d'affichage CIN et Unité")
    print("=" * 60)
    
    success1 = corriger_template()
    success2 = verifier_corrections()
    
    print("\n" + "=" * 60)
    if success1 and success2:
        print("🎉 CORRECTION RÉUSSIE")
        print("✅ Le template affiche maintenant correctement:")
        print("   - Le numéro de CIN")
        print("   - Le libellé de l'unité")
        print("   - Le libellé de l'arme")
    else:
        print("❌ CORRECTION ÉCHOUÉE")
        print("⚠️ Vérifiez manuellement le template")
