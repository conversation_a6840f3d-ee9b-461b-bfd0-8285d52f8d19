#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Test pour vérifier l'affichage des champs CIN et unité
"""

import sys
import os
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from app import app
from rh_models import Personnel
from db import db

def test_champs_affichage():
    """Test des champs CIN et unité dans l'affichage"""
    
    with app.app_context():
        try:
            # Récupérer quelques militaires avec leurs relations
            militaires = Personnel.query.join(Personnel.arme)\
                                      .join(Personnel.unite)\
                                      .join(Personnel.grade_actuel)\
                                      .join(Personnel.categorie)\
                                      .order_by(Personnel.nom, Personnel.prenom)\
                                      .limit(5).all()
            
            print("🧪 Test des champs d'affichage")
            print("=" * 60)
            
            for i, militaire in enumerate(militaires, 1):
                print(f"\n👤 Militaire {i}: {militaire.nom} {militaire.prenom}")
                print(f"   📋 Matricule: {militaire.matricule}")
                
                # Test CIN
                print(f"   🆔 CIN:")
                print(f"      - numero_cin: {getattr(militaire, 'numero_cin', 'CHAMP MANQUANT')}")
                print(f"      - date_expiration_cin: {getattr(militaire, 'date_expiration_cin', 'CHAMP MANQUANT')}")
                
                # Test Unité
                print(f"   🏢 Unité:")
                if hasattr(militaire, 'unite') and militaire.unite:
                    print(f"      - unite.libelle: {militaire.unite.libelle}")
                    print(f"      - unite.code: {getattr(militaire.unite, 'code', 'CHAMP MANQUANT')}")
                else:
                    print(f"      - unite: RELATION MANQUANTE")
                
                # Test Arme (Service)
                print(f"   ⚔️ Arme:")
                if hasattr(militaire, 'arme') and militaire.arme:
                    print(f"      - arme.libelle: {militaire.arme.libelle}")
                    print(f"      - arme.code: {getattr(militaire.arme, 'code', 'CHAMP MANQUANT')}")
                else:
                    print(f"      - arme: RELATION MANQUANTE")
                
                # Test Grade
                print(f"   🎖️ Grade:")
                if hasattr(militaire, 'grade_actuel') and militaire.grade_actuel:
                    print(f"      - grade_actuel.libelle: {militaire.grade_actuel.libelle}")
                else:
                    print(f"      - grade_actuel: RELATION MANQUANTE")
                
                print("-" * 40)
            
            return True
            
        except Exception as e:
            print(f"❌ Erreur lors du test : {str(e)}")
            import traceback
            traceback.print_exc()
            return False

def test_structure_modele():
    """Test de la structure du modèle Personnel"""
    
    with app.app_context():
        try:
            print("\n🔍 Structure du modèle Personnel")
            print("=" * 60)
            
            # Récupérer un militaire
            militaire = Personnel.query.first()
            
            if not militaire:
                print("❌ Aucun militaire trouvé dans la base")
                return False
            
            print(f"👤 Test avec: {militaire.nom} {militaire.prenom}")
            
            # Lister tous les attributs
            print("\n📋 Attributs disponibles:")
            attributs = [attr for attr in dir(militaire) if not attr.startswith('_')]
            
            for attr in sorted(attributs):
                try:
                    valeur = getattr(militaire, attr)
                    if not callable(valeur):
                        print(f"   ✅ {attr}: {type(valeur).__name__}")
                except Exception as e:
                    print(f"   ❌ {attr}: ERREUR - {str(e)}")
            
            # Test spécifique des champs problématiques
            print("\n🎯 Test des champs spécifiques:")
            
            champs_test = [
                'numero_cin',
                'date_expiration_cin', 
                'date_delivrance_cin',
                'unite',
                'arme',
                'grade_actuel'
            ]
            
            for champ in champs_test:
                try:
                    valeur = getattr(militaire, champ)
                    if hasattr(valeur, 'libelle'):
                        print(f"   ✅ {champ}: {valeur.libelle}")
                    elif hasattr(valeur, 'code'):
                        print(f"   ✅ {champ}: {valeur.code}")
                    else:
                        print(f"   ✅ {champ}: {valeur}")
                except Exception as e:
                    print(f"   ❌ {champ}: ERREUR - {str(e)}")
            
            return True
            
        except Exception as e:
            print(f"❌ Erreur lors du test de structure : {str(e)}")
            return False

def test_requete_avec_jointures():
    """Test de la requête avec jointures comme dans rh_blueprint.py"""
    
    with app.app_context():
        try:
            print("\n🔗 Test de la requête avec jointures")
            print("=" * 60)
            
            # Requête identique à celle de rh_blueprint.py
            personnel_paginated = Personnel.query.join(Personnel.arme)\
                                                .join(Personnel.unite)\
                                                .join(Personnel.grade_actuel)\
                                                .join(Personnel.categorie)\
                                                .order_by(Personnel.nom, Personnel.prenom)\
                                                .paginate(page=1, per_page=3, error_out=False)
            
            print(f"📊 Résultats: {len(personnel_paginated.items)} militaires")
            
            for militaire in personnel_paginated.items:
                print(f"\n👤 {militaire.nom} {militaire.prenom}")
                print(f"   🆔 CIN: {militaire.numero_cin}")
                print(f"   🏢 Unité: {militaire.unite.libelle}")
                print(f"   ⚔️ Arme: {militaire.arme.libelle}")
                print(f"   🎖️ Grade: {militaire.grade_actuel.libelle}")
            
            return True
            
        except Exception as e:
            print(f"❌ Erreur lors du test de requête : {str(e)}")
            import traceback
            traceback.print_exc()
            return False

if __name__ == "__main__":
    print("🧪 Test des champs d'affichage CIN et Unité")
    print("=" * 60)
    
    success1 = test_champs_affichage()
    success2 = test_structure_modele()
    success3 = test_requete_avec_jointures()
    
    print("\n" + "=" * 60)
    if success1 and success2 and success3:
        print("🎉 TOUS LES TESTS RÉUSSIS")
        print("✅ Les champs CIN et Unité sont accessibles")
    else:
        print("❌ CERTAINS TESTS ONT ÉCHOUÉ")
        print("⚠️ Problème d'accès aux champs CIN et/ou Unité")
