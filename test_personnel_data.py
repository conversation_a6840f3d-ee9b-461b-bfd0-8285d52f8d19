#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Test de l'objet PersonnelData
"""

def test_personnel_data():
    """Test de la classe PersonnelData"""
    
    # Simuler des données de personnel
    class MockPersonnel:
        def __init__(self, matricule, nom, prenom):
            self.matricule = matricule
            self.nom = nom
            self.prenom = prenom
    
    # Créer des données de test
    test_data = [
        MockPersonnel("1056663", "OUALI", "DRISS"),
        MockPersonnel("1067602", "BENALI", "AHMED"),
        MockPersonnel("1122634", "BERRADA", "ZAKARIA")
    ]
    
    # Créer la classe PersonnelData comme dans rh_blueprint.py
    class PersonnelData:
        def __init__(self, items):
            self.items = items
            self.total = len(items)
            # Attributs pour la pagination (même si non utilisés)
            self.pages = 1
            self.page = 1
            self.per_page = len(items)
            self.has_prev = False
            self.has_next = False
    
    # Tester l'objet
    personnel_obj = PersonnelData(test_data)
    
    print("=" * 50)
    print("TEST DE L'OBJET PERSONNELDATA")
    print("=" * 50)
    
    print(f"Type de personnel_obj.items: {type(personnel_obj.items)}")
    print(f"Nombre d'items: {len(personnel_obj.items)}")
    print(f"Total: {personnel_obj.total}")
    print(f"Pages: {personnel_obj.pages}")
    
    print("\nTest d'itération sur personnel_obj.items:")
    try:
        for i, militaire in enumerate(personnel_obj.items):
            print(f"  {i+1}. {militaire.matricule} - {militaire.nom} {militaire.prenom}")
        print("✅ Itération réussie")
    except Exception as e:
        print(f"❌ Erreur d'itération: {e}")
    
    print("\nTest d'accès aux attributs:")
    try:
        print(f"  personnel_obj.items est itérable: {hasattr(personnel_obj.items, '__iter__')}")
        print(f"  personnel_obj.pages: {personnel_obj.pages}")
        print(f"  personnel_obj.total: {personnel_obj.total}")
        print("✅ Accès aux attributs réussi")
    except Exception as e:
        print(f"❌ Erreur d'accès: {e}")
    
    # Test de template simulation
    print("\nSimulation de template Jinja2:")
    try:
        # Simuler ce que fait le template
        if personnel_obj and personnel_obj.items:
            print("  Condition 'if personnel_obj and personnel_obj.items': ✅")
            for militaire in personnel_obj.items:
                print(f"    - {militaire.nom}")
            print("  Boucle 'for militaire in personnel_obj.items': ✅")
        
        if personnel_obj.pages > 1:
            print("  Pagination détectée")
        else:
            print("  Pas de pagination (pages = 1): ✅")
            
        print("✅ Simulation template réussie")
    except Exception as e:
        print(f"❌ Erreur simulation template: {e}")

if __name__ == "__main__":
    test_personnel_data()
