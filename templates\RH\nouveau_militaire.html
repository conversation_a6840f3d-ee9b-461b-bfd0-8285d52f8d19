{% extends "rh/base_rh.html" %}

{% block title %}Nouveau Militaire - Gestion RH{% endblock %}

{% block content %}
<div class="fade-in">
    <!-- En-tête -->
    <div class="row mb-4">
        <div class="col-12">
            <div class="card-military">
                <div class="card-header-military">
                    <div class="row align-items-center">
                        <div class="col-md-8">
                            <h2 class="mb-0">
                                <i class="fas fa-user-plus"></i>
                                Nouveau Militaire
                            </h2>
                            <small style="color: var(--text-light);">Enregistrement d'un nouveau militaire dans le système RH</small>
                        </div>
                        <div class="col-md-4 text-end">
                            <a href="{{ url_for('rh.recherche_personnel') }}" class="btn btn-info">
                                <i class="fas fa-arrow-left"></i> Retour à la Recherche
                            </a>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Formulaire -->
    <form method="POST">
        <div class="row">
            <!-- Informations Personnelles -->
            <div class="col-lg-6 mb-4">
                <div class="card-military">
                    <div class="card-header-military">
                        <h5 class="mb-0">
                            <i class="fas fa-user"></i>
                            Informations Personnelles
                        </h5>
                    </div>
                    <div class="card-body">
                        <div class="row g-3">
                            <div class="col-md-6">
                                <label class="form-label fw-bold">Matricule</label>
                                <input type="text" name="matricule" class="form-control"
                                       placeholder="Ex: ART123456">
                            </div>
                            <div class="col-md-6">
                                <label class="form-label fw-bold">Genre</label>
                                <select name="sexe_id" class="form-control">
                                    <option value="">Sélectionner...</option>
                                    {% for genre in genres %}
                                    <option value="{{ genre.id_genre }}">{{ genre.libelle }}</option>
                                    {% endfor %}
                                </select>
                            </div>
                            <div class="col-md-6">
                                <label class="form-label fw-bold">Nom</label>
                                <input type="text" name="nom" class="form-control">
                            </div>
                            <div class="col-md-6">
                                <label class="form-label fw-bold">Prénom</label>
                                <input type="text" name="prenom" class="form-control">
                            </div>
                            <div class="col-md-6">
                                <label class="form-label fw-bold">Nom (Arabe)</label>
                                <input type="text" name="nom_arabe" class="form-control" dir="rtl">
                            </div>
                            <div class="col-md-6">
                                <label class="form-label fw-bold">Prénom (Arabe)</label>
                                <input type="text" name="prenom_arabe" class="form-control" dir="rtl">
                            </div>
                            <div class="col-md-6">
                                <label class="form-label fw-bold">Date de Naissance</label>
                                <input type="date" name="date_naissance" class="form-control">
                            </div>
                            <div class="col-md-6">
                                <label class="form-label fw-bold">Lieu de Naissance</label>
                                <input type="text" name="lieu_naissance" class="form-control">
                            </div>
                            <div class="col-md-6">
                                <label class="form-label fw-bold">Groupe Sanguin</label>
                                <select name="groupe_sanguin_id" class="form-control">
                                    <option value="">Sélectionner...</option>
                                    {% for groupe in groupes_sanguins %}
                                    <option value="{{ groupe.id_groupe }}">{{ groupe.libelle }}</option>
                                    {% endfor %}
                                </select>
                            </div>
                            <div class="col-md-6">
                                <label class="form-label fw-bold">Taille (cm)</label>
                                <input type="number" name="taille" class="form-control" min="140" max="220" step="0.01">
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Informations Militaires -->
            <div class="col-lg-6 mb-4">
                <div class="card-military">
                    <div class="card-header-military">
                        <h5 class="mb-0">
                            <i class="fas fa-shield-alt"></i>
                            Informations Militaires
                        </h5>
                    </div>
                    <div class="card-body">
                        <div class="row g-3">
                            <div class="col-md-6">
                                <label class="form-label fw-bold">Grade Actuel</label>
                                <select name="grade_actuel_id" class="form-control" id="gradeSelectNouveau">
                                    <option value="">Sélectionner...</option>
                                    {% for grade in grades %}
                                    <option value="{{ grade.id_grade }}">{{ grade.libelle }}</option>
                                    {% endfor %}
                                </select>
                            </div>
                            <div class="col-md-6">
                                <label class="form-label fw-bold">Catégorie (automatique)</label>
                                <input type="text" class="form-control" id="categorieAutoNouveau" readonly>
                                <input type="hidden" name="categorie_id" id="categorieIdNouveau">
                            </div>
                            <div class="col-md-6">
                                <label class="form-label fw-bold">Service/Arme</label>
                                <select name="arme_id" class="form-control" id="service_select">
                                    <option value="">Sélectionner...</option>
                                    {% for service in services %}
                                    <option value="{{ service.id_arme }}">{{ service.libelle }}</option>
                                    {% endfor %}
                                </select>
                            </div>
                            <div class="col-md-6">
                                <label class="form-label fw-bold">Spécialité</label>
                                <select name="specialite_id" class="form-control" id="specialite_select">
                                    <option value="">Sélectionner d'abord un service...</option>
                                </select>
                            </div>
                            <div class="col-md-6">
                                <label class="form-label fw-bold">Unité</label>
                                <select name="unite_id" class="form-control">
                                    <option value="">Sélectionner...</option>
                                    {% for unite in unites %}
                                    <option value="{{ unite.id_unite }}">{{ unite.libelle }}</option>
                                    {% endfor %}
                                </select>
                            </div>
                            <div class="col-md-6">
                                <label class="form-label fw-bold">Date d'Engagement</label>
                                <input type="date" name="date_engagement" class="form-control">
                            </div>
                            <div class="col-md-12">
                                <label class="form-label fw-bold">Fonction</label>
                                <input type="text" name="fonction" class="form-control"
                                       placeholder="Ex: Chef de Section, Opérateur Radio...">
                            </div>
                            <div class="col-md-12">
                                <label class="form-label fw-bold">Date de Prise de Fonction</label>
                                <input type="date" name="date_prise_fonction" class="form-control">
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <div class="row">
            <!-- Documents d'Identité -->
            <div class="col-lg-6 mb-4">
                <div class="card-military">
                    <div class="card-header-military">
                        <h5 class="mb-0">
                            <i class="fas fa-id-card"></i>
                            Documents d'Identité
                        </h5>
                    </div>
                    <div class="card-body">
                        <div class="row g-3">
                            <div class="col-md-12">
                                <label class="form-label fw-bold">Numéro CIN</label>
                                <input type="text" name="numero_cin" class="form-control">
                            </div>
                            <div class="col-md-6">
                                <label class="form-label fw-bold">Date de Délivrance CIN</label>
                                <input type="date" name="date_delivrance_cin" class="form-control">
                            </div>
                            <div class="col-md-6">
                                <label class="form-label fw-bold">Date d'Expiration CIN</label>
                                <input type="date" name="date_expiration_cin" class="form-control">
                            </div>
                            <div class="col-md-12">
                                <label class="form-label fw-bold">Numéro Passeport</label>
                                <input type="text" name="numero_passport" class="form-control">
                            </div>
                            <div class="col-md-6">
                                <label class="form-label fw-bold">Date Délivrance Passeport</label>
                                <input type="date" name="date_delivrance_passport" class="form-control">
                            </div>
                            <div class="col-md-6">
                                <label class="form-label fw-bold">Date Expiration Passeport</label>
                                <input type="date" name="date_expiration_passport" class="form-control">
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Coordonnées -->
            <div class="col-lg-6 mb-4">
                <div class="card-military">
                    <div class="card-header-military">
                        <h5 class="mb-0">
                            <i class="fas fa-phone"></i>
                            Coordonnées
                        </h5>
                    </div>
                    <div class="card-body">
                        <div class="row g-3">
                            <div class="col-md-6">
                                <label class="form-label fw-bold">GSM</label>
                                <input type="tel" name="gsm" class="form-control"
                                       placeholder="06XXXXXXXX">
                            </div>
                            <div class="col-md-6">
                                <label class="form-label fw-bold">Téléphone Domicile</label>
                                <input type="tel" name="telephone_domicile" class="form-control"
                                       placeholder="05XXXXXXXX">
                            </div>
                            <div class="col-md-12">
                                <label class="form-label fw-bold">Lieu de Résidence</label>
                                <textarea name="lieu_residence" class="form-control" rows="2"
                                          placeholder="Adresse complète de résidence"></textarea>
                            </div>
                            <div class="col-md-6">
                                <label class="form-label fw-bold">GSM d'Urgence</label>
                                <input type="tel" name="gsm_urgence" class="form-control"
                                       placeholder="06XXXXXXXX">
                            </div>
                            <div class="col-md-6">
                                <label class="form-label fw-bold">Lien de Parenté</label>
                                <select name="degre_parente_id" class="form-control">
                                    <option value="">Sélectionner...</option>
                                    {% for lien in liens_parente %}
                                    <option value="{{ lien.id_degre }}">{{ lien.libelle }}</option>
                                    {% endfor %}
                                </select>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <div class="row">
            <!-- Informations Familiales -->
            <div class="col-lg-6 mb-4">
                <div class="card-military">
                    <div class="card-header-military">
                        <h5 class="mb-0">
                            <i class="fas fa-home"></i>
                            Informations Familiales
                        </h5>
                    </div>
                    <div class="card-body">
                        <div class="row g-3">
                            <div class="col-md-6">
                                <label class="form-label fw-bold">Nom du Père</label>
                                <input type="text" name="nom_pere" class="form-control">
                            </div>
                            <div class="col-md-6">
                                <label class="form-label fw-bold">Prénom du Père</label>
                                <input type="text" name="prenom_pere" class="form-control">
                            </div>
                            <div class="col-md-6">
                                <label class="form-label fw-bold">Nom de la Mère</label>
                                <input type="text" name="nom_mere" class="form-control">
                            </div>
                            <div class="col-md-6">
                                <label class="form-label fw-bold">Prénom de la Mère</label>
                                <input type="text" name="prenom_mere" class="form-control">
                            </div>
                            <div class="col-md-12">
                                <label class="form-label fw-bold">Adresse des Parents</label>
                                <textarea name="adresse_parents" class="form-control" rows="2"
                                          placeholder="Adresse complète des parents"></textarea>
                            </div>
                            <div class="col-md-6">
                                <label class="form-label fw-bold">État Matrimonial</label>
                                <select name="situation_fam_id" class="form-control">
                                    <option value="">Sélectionner...</option>
                                    {% for etat in etats_matrimoniaux %}
                                    <option value="{{ etat.id_sitfam }}">{{ etat.libelle }}</option>
                                    {% endfor %}
                                </select>
                            </div>
                            <div class="col-md-6">
                                <label class="form-label fw-bold">Nombre d'Enfants</label>
                                <input type="number" name="nombre_enfants" class="form-control" min="0" max="20">
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Informations Financières -->
            <div class="col-lg-6 mb-4">
                <div class="card-military">
                    <div class="card-header-military">
                        <h5 class="mb-0">
                            <i class="fas fa-credit-card"></i>
                            Informations Financières
                        </h5>
                    </div>
                    <div class="card-body">
                        <div class="row g-3">
                            <div class="col-md-12">
                                <label class="form-label fw-bold">Numéro CCP</label>
                                <input type="text" name="ccp" class="form-control">
                            </div>
                            <div class="col-md-12">
                                <label class="form-label fw-bold">Numéro Compte Bancaire</label>
                                <input type="text" name="compte_bancaire" class="form-control">
                            </div>
                            <div class="col-md-12">
                                <label class="form-label fw-bold">Numéro SOMME</label>
                                <input type="text" name="numero_somme" class="form-control">
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- Boutons d'Action -->
        <div class="row">
            <div class="col-12">
                <div class="card-military">
                    <div class="card-body text-center">
                        <button type="submit" class="btn btn-success-military btn-lg me-3">
                            <i class="fas fa-save"></i> Enregistrer le Militaire
                        </button>
                        <a href="{{ url_for('rh.recherche_personnel') }}" class="btn btn-secondary btn-lg">
                            <i class="fas fa-times"></i> Annuler
                        </a>
                    </div>
                </div>
            </div>
        </div>
    </form>
</div>
{% endblock %}

{% block extra_js %}
<script>
// Validation du formulaire
(function() {
    'use strict';
    window.addEventListener('load', function() {
        var forms = document.getElementsByClassName('needs-validation');
        var validation = Array.prototype.filter.call(forms, function(form) {
            form.addEventListener('submit', function(event) {
                if (form.checkValidity() === false) {
                    event.preventDefault();
                    event.stopPropagation();
                }
                form.classList.add('was-validated');
            }, false);
        });
    }, false);
})();

// Gestion des spécialités selon le service sélectionné
document.getElementById('service_select').addEventListener('change', function() {
    const serviceId = this.value;
    const specialiteSelect = document.getElementById('specialite_select');

    // Réinitialiser les spécialités
    specialiteSelect.innerHTML = '<option value="">Chargement...</option>';

    if (serviceId) {
        // Appel AJAX pour charger les spécialités du service sélectionné
        fetch(`/rh/api/specialites/${serviceId}`)
            .then(response => response.json())
            .then(data => {
                specialiteSelect.innerHTML = '<option value="">Aucune spécialité spécifique</option>';
                if (data.success && data.specialites) {
                    data.specialites.forEach(specialite => {
                        const option = document.createElement('option');
                        option.value = specialite.id;
                        option.textContent = specialite.libelle;
                        specialiteSelect.appendChild(option);
                    });
                }
            })
            .catch(error => {
                console.error('Erreur lors du chargement des spécialités:', error);
                specialiteSelect.innerHTML = '<option value="">Erreur de chargement</option>';
            });
    } else {
        specialiteSelect.innerHTML = '<option value="">Sélectionner d\'abord un service...</option>';
    }
});

// Auto-remplissage de la date de prise de fonction avec la date d'engagement
document.querySelector('input[name="date_engagement"]').addEventListener('change', function() {
    const datePriseFonction = document.querySelector('input[name="date_prise_fonction"]');
    if (!datePriseFonction.value) {
        datePriseFonction.value = this.value;
    }
});

// Validation des numéros de téléphone marocains
function validateMoroccanPhone(input) {
    const phonePattern = /^(06|07|05)\d{8}$/;
    if (input.value && !phonePattern.test(input.value.replace(/\s/g, ''))) {
        input.setCustomValidity('Format invalide (ex: 06XXXXXXXX)');
    } else {
        input.setCustomValidity('');
    }
}

document.querySelectorAll('input[type="tel"]').forEach(input => {
    input.addEventListener('input', () => validateMoroccanPhone(input));
});

// Logique automatique Grade → Catégorie pour nouveau militaire
const gradeSelectNouveau = document.getElementById('gradeSelectNouveau');
const categorieAutoNouveau = document.getElementById('categorieAutoNouveau');
const categorieIdNouveau = document.getElementById('categorieIdNouveau');

// Règles de liaison Grade → Catégorie
const gradeCategoriesNouveau = {
    // Officier
    'sous-lieutenant': { libelle: 'Officier', id: 1 },
    'lieutenant': { libelle: 'Officier', id: 1 },
    'capitaine': { libelle: 'Officier', id: 1 },
    'commandant': { libelle: 'Officier', id: 1 },
    'lt-colonel': { libelle: 'Officier', id: 1 },
    'colonel': { libelle: 'Officier', id: 1 },

    // Officier du rang
    'MDL': { libelle: 'Officier du rang', id: 2 },
    'MDL Chef': { libelle: 'Officier du rang', id: 2 },
    'adjudant': { libelle: 'Officier du rang', id: 2 },
    'adjudant chef': { libelle: 'Officier du rang', id: 2 },

    // Militaire du rang
    'soldat 1°classe': { libelle: 'Militaire du rang', id: 3 },
    'soldat 2° classe': { libelle: 'Militaire du rang', id: 3 },
    'brigadier': { libelle: 'Militaire du rang', id: 3 },
    'brigadier chef': { libelle: 'Militaire du rang', id: 3 }
};

if (gradeSelectNouveau) {
    gradeSelectNouveau.addEventListener('change', function() {
        const selectedOption = this.options[this.selectedIndex];
        const gradeLibelle = selectedOption.textContent.trim();

        // Trouver la catégorie correspondante
        if (gradeCategoriesNouveau[gradeLibelle]) {
            const categorie = gradeCategoriesNouveau[gradeLibelle];
            categorieAutoNouveau.value = categorie.libelle;
            categorieIdNouveau.value = categorie.id;
        } else {
            categorieAutoNouveau.value = '';
            categorieIdNouveau.value = '';
        }
    });
}
</script>
{% endblock %}
