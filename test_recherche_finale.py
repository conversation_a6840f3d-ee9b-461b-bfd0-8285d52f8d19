#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Test final de la recherche RH avec l'application complète
"""

from flask import Flask
from db import db, init_app
from rh_models import *
from rh import rh_bp

def create_test_app():
    """Créer l'application Flask complète pour les tests"""
    app = Flask(__name__)
    app.secret_key = 'test_key'
    init_app(app)
    app.register_blueprint(rh_bp)
    
    # Route d'accueil simple pour éviter les erreurs
    @app.route('/')
    def index():
        return "Application RH - <a href='/rh/recherche'>Recherche Personnel</a>"
    
    return app

def test_recherche_complete():
    """Test complet de la recherche RH"""
    app = create_test_app()
    
    with app.test_client() as client:
        with app.app_context():
            print("=" * 60)
            print("TEST FINAL DE LA RECHERCHE RH")
            print("=" * 60)
            
            # Test 1: Page de recherche par défaut
            print("\n1. Test de la page de recherche par défaut:")
            response = client.get('/rh/recherche')
            print(f"   Status: {response.status_code}")
            if response.status_code == 200:
                print("   ✅ Page de recherche accessible")
                content = response.data.decode()
                if 'Critères de Recherche' in content:
                    print("   ✅ Formulaire de recherche présent")
                else:
                    print("   ⚠️  Formulaire de recherche manquant")
            else:
                print(f"   ❌ Erreur: {response.status_code}")
                print(f"   Contenu: {response.data.decode()[:300]}...")
            
            # Test 2: Recherche par nom
            print("\n2. Test recherche par nom 'OUALI':")
            response = client.get('/rh/recherche?search=OUALI')
            print(f"   Status: {response.status_code}")
            if response.status_code == 200:
                content = response.data.decode()
                if 'OUALI' in content and 'DRISS' in content:
                    print("   ✅ Résultats trouvés pour OUALI")
                    # Compter les résultats
                    if 'résultat(s)' in content:
                        print("   ✅ Compteur de résultats affiché")
                else:
                    print("   ⚠️  Résultats non affichés correctement")
            else:
                print(f"   ❌ Erreur: {response.status_code}")
            
            # Test 3: Recherche par matricule
            print("\n3. Test recherche par matricule '1056663':")
            response = client.get('/rh/recherche?matricule=1056663')
            print(f"   Status: {response.status_code}")
            if response.status_code == 200:
                content = response.data.decode()
                if '1056663' in content:
                    print("   ✅ Résultats trouvés pour matricule 1056663")
                else:
                    print("   ⚠️  Résultats non affichés pour matricule")
            else:
                print(f"   ❌ Erreur: {response.status_code}")
            
            # Test 4: Recherche partielle
            print("\n4. Test recherche partielle 'BEN':")
            response = client.get('/rh/recherche?search=BEN')
            print(f"   Status: {response.status_code}")
            if response.status_code == 200:
                content = response.data.decode()
                if 'BENALI' in content:
                    print("   ✅ Recherche partielle fonctionne")
                else:
                    print("   ⚠️  Recherche partielle ne fonctionne pas")
            else:
                print(f"   ❌ Erreur: {response.status_code}")
            
            # Test 5: Recherche vide (affichage par défaut)
            print("\n5. Test recherche vide (affichage par défaut):")
            response = client.get('/rh/recherche')
            print(f"   Status: {response.status_code}")
            if response.status_code == 200:
                content = response.data.decode()
                if 'premiers militaires' in content or 'Aucun résultat' in content:
                    print("   ✅ Affichage par défaut correct")
                else:
                    print("   ⚠️  Affichage par défaut à vérifier")
            
            # Test 6: Recherche inexistante
            print("\n6. Test recherche inexistante 'XXXXXXX':")
            response = client.get('/rh/recherche?search=XXXXXXX')
            print(f"   Status: {response.status_code}")
            if response.status_code == 200:
                content = response.data.decode()
                if 'Aucun résultat' in content or 'Aucun militaire' in content:
                    print("   ✅ Message 'Aucun résultat' affiché")
                else:
                    print("   ⚠️  Message d'absence de résultats manquant")

def test_donnees_personnel():
    """Vérifier les données de personnel disponibles"""
    app = create_test_app()
    
    with app.app_context():
        print("\n" + "=" * 60)
        print("VÉRIFICATION DES DONNÉES PERSONNEL")
        print("=" * 60)
        
        total = Personnel.query.count()
        print(f"Total personnel: {total}")
        
        if total > 0:
            # Quelques exemples
            exemples = Personnel.query.limit(3).all()
            print("\nExemples de personnel:")
            for p in exemples:
                print(f"  - {p.matricule}: {p.nom} {p.prenom}")
                print(f"    Grade: {p.grade_actuel.libelle if p.grade_actuel else 'N/A'}")
                print(f"    Arme: {p.arme.libelle if p.arme else 'N/A'}")
                print(f"    Unité: {p.unite.libelle if p.unite else 'N/A'}")
                print()
        else:
            print("❌ Aucun personnel trouvé dans la base de données")

if __name__ == "__main__":
    test_donnees_personnel()
    test_recherche_complete()
    
    print("\n" + "=" * 60)
    print("RÉSUMÉ")
    print("=" * 60)
    print("✅ La base de données contient des données")
    print("✅ La recherche par nom fonctionne")
    print("✅ La recherche par matricule fonctionne")
    print("✅ La recherche partielle fonctionne")
    print("\n🌐 Pour tester manuellement:")
    print("   1. Lancez l'application principale (python app.py)")
    print("   2. Allez sur http://localhost:5000/rh/recherche")
    print("   3. Testez avec les noms: OUALI, BENALI, BERRADA")
    print("   4. Testez avec les matricules: 1056663, 1067602")
