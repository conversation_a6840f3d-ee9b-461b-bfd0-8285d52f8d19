#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Solution finale pour la recherche RH
Ajoute une route de recherche simple qui fonctionne
"""

from flask import Flask, request, render_template_string, jsonify
from db import db, init_app
from rh_models import *
from sqlalchemy import or_

# Template HTML simple et fonctionnel
TEMPLATE_RECHERCHE_SIMPLE = """
<!DOCTYPE html>
<html lang="fr">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Recherche Personnel RH</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
    <style>
        .military-header { background: linear-gradient(135deg, #2c3e50, #34495e); color: white; }
        .military-card { border-left: 4px solid #27ae60; }
        .badge-military { background-color: #27ae60; }
    </style>
</head>
<body class="bg-light">
    <div class="container-fluid">
        <!-- En-tête -->
        <div class="military-header p-4 mb-4">
            <div class="row align-items-center">
                <div class="col-md-8">
                    <h1><i class="fas fa-search me-2"></i>Recherche Personnel RH</h1>
                    <p class="mb-0">Système de gestion des ressources humaines militaires</p>
                </div>
                <div class="col-md-4 text-end">
                    <span class="badge badge-military fs-6">
                        <i class="fas fa-users me-1"></i>{{ total_personnel }} militaires
                    </span>
                </div>
            </div>
        </div>

        <!-- Formulaire de recherche -->
        <div class="row mb-4">
            <div class="col-12">
                <div class="card military-card">
                    <div class="card-header bg-primary text-white">
                        <h5 class="mb-0"><i class="fas fa-filter me-2"></i>Critères de Recherche</h5>
                    </div>
                    <div class="card-body">
                        <form method="GET" action="/recherche_simple">
                            <div class="row g-3">
                                <div class="col-md-4">
                                    <label class="form-label fw-bold">Nom/Prénom</label>
                                    <input type="text" name="search" class="form-control" 
                                           placeholder="Rechercher par nom ou prénom..." 
                                           value="{{ search_term or '' }}">
                                </div>
                                <div class="col-md-4">
                                    <label class="form-label fw-bold">Matricule</label>
                                    <input type="text" name="matricule" class="form-control" 
                                           placeholder="Ex: 1056663..." 
                                           value="{{ matricule or '' }}">
                                </div>
                                <div class="col-md-4">
                                    <label class="form-label">&nbsp;</label>
                                    <div class="d-grid gap-2 d-md-flex">
                                        <button type="submit" class="btn btn-primary">
                                            <i class="fas fa-search me-1"></i>Rechercher
                                        </button>
                                        <a href="/recherche_simple" class="btn btn-outline-secondary">
                                            <i class="fas fa-redo me-1"></i>Réinitialiser
                                        </a>
                                    </div>
                                </div>
                            </div>
                        </form>
                    </div>
                </div>
            </div>
        </div>

        <!-- Résultats -->
        <div class="row">
            <div class="col-12">
                <div class="card">
                    <div class="card-header bg-success text-white">
                        <div class="row align-items-center">
                            <div class="col-md-8">
                                <h5 class="mb-0">
                                    <i class="fas fa-list me-2"></i>Résultats de la recherche
                                </h5>
                            </div>
                            <div class="col-md-4 text-end">
                                {% if resultats %}
                                    <span class="badge bg-light text-dark fs-6">
                                        {{ resultats|length }} résultat(s) trouvé(s)
                                    </span>
                                {% endif %}
                            </div>
                        </div>
                    </div>
                    <div class="card-body p-0">
                        {% if resultats %}
                            <div class="table-responsive">
                                <table class="table table-striped table-hover mb-0">
                                    <thead class="table-dark">
                                        <tr>
                                            <th><i class="fas fa-id-card me-1"></i>Matricule</th>
                                            <th><i class="fas fa-user me-1"></i>Nom Complet</th>
                                            <th><i class="fas fa-star me-1"></i>Grade</th>
                                            <th><i class="fas fa-shield-alt me-1"></i>Arme</th>
                                            <th><i class="fas fa-building me-1"></i>Unité</th>
                                            <th><i class="fas fa-briefcase me-1"></i>Fonction</th>
                                        </tr>
                                    </thead>
                                    <tbody>
                                        {% for p in resultats %}
                                        <tr>
                                            <td><strong class="text-primary">{{ p.matricule }}</strong></td>
                                            <td>
                                                <div>
                                                    <strong>{{ p.nom }} {{ p.prenom }}</strong>
                                                    {% if p.nom_arabe and p.prenom_arabe %}
                                                        <br><small class="text-muted">{{ p.nom_arabe }} {{ p.prenom_arabe }}</small>
                                                    {% endif %}
                                                </div>
                                            </td>
                                            <td>
                                                {% if p.grade_actuel %}
                                                    <span class="badge bg-info">{{ p.grade_actuel.libelle }}</span>
                                                {% else %}
                                                    <span class="text-muted">N/A</span>
                                                {% endif %}
                                            </td>
                                            <td>
                                                {% if p.arme %}
                                                    <span class="badge bg-warning text-dark">{{ p.arme.libelle }}</span>
                                                {% else %}
                                                    <span class="text-muted">N/A</span>
                                                {% endif %}
                                            </td>
                                            <td>
                                                {% if p.unite %}
                                                    {{ p.unite.libelle }}
                                                {% else %}
                                                    <span class="text-muted">N/A</span>
                                                {% endif %}
                                            </td>
                                            <td>
                                                <small>{{ p.fonction[:40] if p.fonction else 'N/A' }}{% if p.fonction and p.fonction|length > 40 %}...{% endif %}</small>
                                            </td>
                                        </tr>
                                        {% endfor %}
                                    </tbody>
                                </table>
                            </div>
                        {% else %}
                            <div class="text-center py-5">
                                <i class="fas fa-search fa-4x text-muted mb-3"></i>
                                <h4 class="text-muted">
                                    {% if search_term or matricule %}
                                        Aucun résultat trouvé
                                    {% else %}
                                        Utilisez les filtres ci-dessus pour rechercher du personnel
                                    {% endif %}
                                </h4>
                                {% if search_term or matricule %}
                                    <p class="text-muted">
                                        Aucun militaire ne correspond aux critères de recherche.
                                        <br>Vérifiez l'orthographe ou essayez des termes plus généraux.
                                    </p>
                                {% else %}
                                    <p class="text-muted">
                                        Vous pouvez rechercher par nom, prénom ou matricule.
                                    </p>
                                {% endif %}
                            </div>
                        {% endif %}
                    </div>
                </div>
            </div>
        </div>
    </div>

    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/js/bootstrap.bundle.min.js"></script>
</body>
</html>
"""

def create_solution_app():
    """Créer l'application avec la solution de recherche"""
    app = Flask(__name__)
    app.secret_key = 'solution_key'
    init_app(app)
    
    @app.route('/')
    def index():
        return """
        <h1>Application RH - Solutions de Recherche</h1>
        <ul>
            <li><a href="/recherche_simple">Recherche Simple (Fonctionnelle)</a></li>
            <li><a href="/api/recherche_test">API de Test</a></li>
        </ul>
        """
    
    @app.route('/recherche_simple')
    def recherche_simple():
        """Route de recherche simple et fonctionnelle"""
        try:
            # Récupération des paramètres
            search = request.args.get('search', '').strip()
            matricule = request.args.get('matricule', '').strip()
            
            # Statistiques générales
            total_personnel = Personnel.query.count()
            
            # Construction de la requête
            query = Personnel.query
            resultats = []
            
            # Application des filtres
            if search:
                query = query.filter(
                    or_(
                        Personnel.nom.ilike(f'%{search}%'),
                        Personnel.prenom.ilike(f'%{search}%'),
                        Personnel.nom_arabe.ilike(f'%{search}%'),
                        Personnel.prenom_arabe.ilike(f'%{search}%')
                    )
                )
                resultats = query.all()
            
            elif matricule:
                query = query.filter(Personnel.matricule.ilike(f'%{matricule}%'))
                resultats = query.all()
            
            # Si aucun critère, afficher les 10 premiers
            elif not search and not matricule:
                resultats = Personnel.query.limit(10).all()
            
            return render_template_string(TEMPLATE_RECHERCHE_SIMPLE,
                                        resultats=resultats,
                                        search_term=search,
                                        matricule=matricule,
                                        total_personnel=total_personnel)
                                        
        except Exception as e:
            return f"Erreur: {str(e)}", 500
    
    @app.route('/api/recherche_test')
    def api_recherche_test():
        """API de test pour vérifier les données"""
        try:
            search = request.args.get('search', 'OUALI')
            
            # Recherche
            resultats = Personnel.query.filter(
                or_(
                    Personnel.nom.ilike(f'%{search}%'),
                    Personnel.prenom.ilike(f'%{search}%')
                )
            ).limit(5).all()
            
            # Formatage des résultats
            data = []
            for p in resultats:
                data.append({
                    'matricule': p.matricule,
                    'nom': p.nom,
                    'prenom': p.prenom,
                    'grade': p.grade_actuel.libelle if p.grade_actuel else None,
                    'arme': p.arme.libelle if p.arme else None,
                    'unite': p.unite.libelle if p.unite else None
                })
            
            return jsonify({
                'success': True,
                'search_term': search,
                'count': len(data),
                'results': data
            })
            
        except Exception as e:
            return jsonify({
                'success': False,
                'error': str(e)
            }), 500
    
    return app

def test_solution():
    """Test de la solution"""
    app = create_solution_app()
    
    with app.test_client() as client:
        with app.app_context():
            print("=" * 60)
            print("TEST DE LA SOLUTION DE RECHERCHE")
            print("=" * 60)
            
            # Test 1: Page par défaut
            print("\n1. Test page par défaut:")
            response = client.get('/recherche_simple')
            print(f"   Status: {response.status_code}")
            if response.status_code == 200:
                print("   ✅ Page accessible")
            
            # Test 2: Recherche par nom
            print("\n2. Test recherche 'OUALI':")
            response = client.get('/recherche_simple?search=OUALI')
            print(f"   Status: {response.status_code}")
            if response.status_code == 200:
                content = response.data.decode()
                if 'OUALI' in content and 'DRISS' in content:
                    print("   ✅ Résultats affichés correctement")
                else:
                    print("   ⚠️  Problème d'affichage")
            
            # Test 3: API
            print("\n3. Test API:")
            response = client.get('/api/recherche_test?search=BENALI')
            print(f"   Status: {response.status_code}")
            if response.status_code == 200:
                data = response.get_json()
                print(f"   Résultats API: {data['count']} trouvé(s)")
                print("   ✅ API fonctionne")

if __name__ == "__main__":
    test_solution()
    
    print("\n" + "=" * 60)
    print("SOLUTION PRÊTE")
    print("=" * 60)
    print("✅ Recherche simple fonctionnelle créée")
    print("✅ API de test disponible")
    print("\n🚀 Pour utiliser la solution:")
    print("   1. Lancez: python solution_recherche_rh.py")
    print("   2. Puis dans un autre terminal: python -c \"from solution_recherche_rh import create_solution_app; app = create_solution_app(); app.run(debug=True, port=5001)\"")
    print("   3. Allez sur: http://localhost:5001/recherche_simple")
    print("\n📝 Noms à tester: OUALI, BENALI, BERRADA")
    print("📝 Matricules à tester: 1056663, 1067602")
