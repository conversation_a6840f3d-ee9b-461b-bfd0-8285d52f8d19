#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Script de test pour l'API des spécialités
"""

import requests
import json

def test_api_specialites():
    """Tester l'API des spécialités"""
    base_url = "http://localhost:3000"
    
    print("🔍 Test de l'API des spécialités...")
    
    try:
        # Test 1: API spécialités pour Artillerie (ID: 1)
        print("\n1. Test API spécialités pour Artillerie (ID: 1):")
        response = requests.get(f"{base_url}/rh/api/specialites/1")
        
        if response.status_code == 200:
            data = response.json()
            print(f"✅ Réponse reçue: {json.dumps(data, indent=2, ensure_ascii=False)}")
        else:
            print(f"❌ Erreur HTTP {response.status_code}: {response.text}")
        
        # Test 2: API spécialités pour un service inexistant
        print("\n2. Test API spécialités pour service inexistant (ID: 999):")
        response = requests.get(f"{base_url}/rh/api/specialites/999")
        
        if response.status_code == 200:
            data = response.json()
            print(f"✅ Réponse reçue: {json.dumps(data, indent=2, ensure_ascii=False)}")
        else:
            print(f"❌ Erreur HTTP {response.status_code}: {response.text}")
        
        # Test 3: Tester tous les services
        print("\n3. Test API spécialités pour tous les services:")
        for service_id in range(1, 10):  # Services 1 à 9
            response = requests.get(f"{base_url}/rh/api/specialites/{service_id}")
            if response.status_code == 200:
                data = response.json()
                print(f"  Service {service_id}: {len(data)} spécialité(s)")
                for spec in data:
                    print(f"    - {spec['libelle']} (ID: {spec['id']})")
            else:
                print(f"  Service {service_id}: Erreur {response.status_code}")
        
        print("\n✅ Test de l'API terminé!")
        
    except requests.exceptions.ConnectionError:
        print("❌ Impossible de se connecter au serveur. Assurez-vous que l'application Flask est en cours d'exécution.")
    except Exception as e:
        print(f"❌ Erreur lors du test: {str(e)}")

if __name__ == "__main__":
    test_api_specialites()
