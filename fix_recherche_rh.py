#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Script pour corriger la recherche RH
Crée une route de recherche simple et fonctionnelle
"""

from flask import Flask, request, render_template_string
from db import db, init_app
from rh_models import *
from sqlalchemy import or_

def create_app():
    """Créer l'application Flask pour les tests"""
    app = Flask(__name__)
    app.secret_key = 'test_key'
    init_app(app)
    return app

# Template simple pour tester la recherche
TEMPLATE_RECHERCHE = """
<!DOCTYPE html>
<html>
<head>
    <title>Recherche Personnel RH</title>
    <meta charset="utf-8">
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/css/bootstrap.min.css" rel="stylesheet">
</head>
<body>
    <div class="container mt-4">
        <h2>🔍 Recherche Personnel RH</h2>
        
        <!-- Formulaire de recherche -->
        <div class="card mb-4">
            <div class="card-body">
                <form method="GET">
                    <div class="row">
                        <div class="col-md-4">
                            <label class="form-label">Nom/Prénom</label>
                            <input type="text" name="search" class="form-control" 
                                   placeholder="Rechercher par nom ou prénom..." 
                                   value="{{ search_term or '' }}">
                        </div>
                        <div class="col-md-4">
                            <label class="form-label">Matricule</label>
                            <input type="text" name="matricule" class="form-control" 
                                   placeholder="Ex: 1056663..." 
                                   value="{{ matricule or '' }}">
                        </div>
                        <div class="col-md-4">
                            <label class="form-label">&nbsp;</label>
                            <div>
                                <button type="submit" class="btn btn-primary">
                                    🔍 Rechercher
                                </button>
                                <a href="/recherche_rh" class="btn btn-secondary">
                                    🔄 Réinitialiser
                                </a>
                            </div>
                        </div>
                    </div>
                </form>
            </div>
        </div>
        
        <!-- Résultats -->
        <div class="card">
            <div class="card-header">
                <h5>Résultats de la recherche 
                    {% if resultats %}
                        <span class="badge bg-primary">{{ resultats|length }} résultat(s)</span>
                    {% endif %}
                </h5>
            </div>
            <div class="card-body">
                {% if resultats %}
                    <div class="table-responsive">
                        <table class="table table-striped">
                            <thead>
                                <tr>
                                    <th>Matricule</th>
                                    <th>Nom Complet</th>
                                    <th>Grade</th>
                                    <th>Arme</th>
                                    <th>Unité</th>
                                    <th>Fonction</th>
                                </tr>
                            </thead>
                            <tbody>
                                {% for p in resultats %}
                                <tr>
                                    <td><strong>{{ p.matricule }}</strong></td>
                                    <td>{{ p.nom }} {{ p.prenom }}</td>
                                    <td>
                                        {% if p.grade_actuel %}
                                            <span class="badge bg-info">{{ p.grade_actuel.libelle }}</span>
                                        {% else %}
                                            <span class="text-muted">N/A</span>
                                        {% endif %}
                                    </td>
                                    <td>
                                        {% if p.arme %}
                                            {{ p.arme.libelle }}
                                        {% else %}
                                            <span class="text-muted">N/A</span>
                                        {% endif %}
                                    </td>
                                    <td>
                                        {% if p.unite %}
                                            {{ p.unite.libelle }}
                                        {% else %}
                                            <span class="text-muted">N/A</span>
                                        {% endif %}
                                    </td>
                                    <td>{{ p.fonction or 'N/A' }}</td>
                                </tr>
                                {% endfor %}
                            </tbody>
                        </table>
                    </div>
                {% else %}
                    <div class="text-center py-4">
                        <i class="fas fa-search fa-3x text-muted mb-3"></i>
                        <h5 class="text-muted">
                            {% if search_term or matricule %}
                                Aucun résultat trouvé
                            {% else %}
                                Utilisez les filtres ci-dessus pour rechercher du personnel
                            {% endif %}
                        </h5>
                        {% if search_term or matricule %}
                            <p class="text-muted">
                                Aucun militaire ne correspond aux critères de recherche.
                            </p>
                        {% endif %}
                    </div>
                {% endif %}
            </div>
        </div>
    </div>
</body>
</html>
"""

def test_recherche_simple():
    """Test avec une route de recherche simple"""
    app = create_app()
    
    @app.route('/recherche_rh')
    def recherche_rh():
        """Route de recherche simple et fonctionnelle"""
        try:
            # Récupération des paramètres
            search = request.args.get('search', '').strip()
            matricule = request.args.get('matricule', '').strip()
            
            # Construction de la requête
            query = Personnel.query
            
            # Application des filtres
            if search:
                query = query.filter(
                    or_(
                        Personnel.nom.ilike(f'%{search}%'),
                        Personnel.prenom.ilike(f'%{search}%'),
                        Personnel.nom_arabe.ilike(f'%{search}%'),
                        Personnel.prenom_arabe.ilike(f'%{search}%')
                    )
                )
            
            if matricule:
                query = query.filter(Personnel.matricule.ilike(f'%{matricule}%'))
            
            # Exécution de la requête
            resultats = query.all()
            
            return render_template_string(TEMPLATE_RECHERCHE,
                                        resultats=resultats,
                                        search_term=search,
                                        matricule=matricule)
                                        
        except Exception as e:
            return f"Erreur: {str(e)}", 500
    
    # Test avec le client de test
    with app.test_client() as client:
        with app.app_context():
            print("=" * 60)
            print("TEST DE LA RECHERCHE CORRIGÉE")
            print("=" * 60)
            
            # Test 1: Page par défaut
            print("\n1. Test page par défaut:")
            response = client.get('/recherche_rh')
            print(f"   Status: {response.status_code}")
            if response.status_code == 200:
                print("   ✅ Page accessible")
            
            # Test 2: Recherche par nom
            print("\n2. Test recherche par nom 'OUALI':")
            response = client.get('/recherche_rh?search=OUALI')
            print(f"   Status: {response.status_code}")
            if response.status_code == 200:
                content = response.data.decode()
                if 'OUALI' in content and 'DRISS' in content:
                    print("   ✅ Résultats trouvés pour OUALI")
                else:
                    print("   ⚠️  Problème avec les résultats")
            
            # Test 3: Recherche par matricule
            print("\n3. Test recherche par matricule '1056663':")
            response = client.get('/recherche_rh?matricule=1056663')
            print(f"   Status: {response.status_code}")
            if response.status_code == 200:
                content = response.data.decode()
                if '1056663' in content:
                    print("   ✅ Résultats trouvés pour matricule")
                else:
                    print("   ⚠️  Problème avec les résultats")
            
            # Test 4: Recherche partielle
            print("\n4. Test recherche partielle 'BEN':")
            response = client.get('/recherche_rh?search=BEN')
            print(f"   Status: {response.status_code}")
            if response.status_code == 200:
                content = response.data.decode()
                if 'BENALI' in content:
                    print("   ✅ Recherche partielle fonctionne")
                else:
                    print("   ⚠️  Problème avec la recherche partielle")
    
    print(f"\n🌐 Pour tester manuellement, lancez l'application et allez sur:")
    print(f"   http://localhost:5000/recherche_rh")

if __name__ == "__main__":
    test_recherche_simple()
