#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Test pour vérifier que l'affichage complet fonctionne
"""

import sys
import os
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from app import app
from rh_models import Personnel
from db import db

def test_affichage_complet():
    """Test de l'affichage complet sans limitation"""
    
    with app.app_context():
        try:
            # Compter le total de personnel
            total_personnel = Personnel.query.count()
            print(f"📊 Total personnel dans la base : {total_personnel}")
            
            if total_personnel == 0:
                print("❌ Aucun personnel trouvé dans la base de données")
                return False
            
            # Test de la requête sans limitation (comme dans rh_blueprint.py)
            personnel_defaut = Personnel.query.join(Personnel.arme)\
                                             .join(Personnel.unite)\
                                             .join(Personnel.grade_actuel)\
                                             .join(Personnel.categorie)\
                                             .order_by(Personnel.nom, Personnel.prenom)\
                                             .all()
            
            print(f"✅ Personnel récupéré sans limitation : {len(personnel_defaut)}")
            
            # Test de recherche sans limitation
            resultats_recherche = Personnel.query.order_by(Personnel.nom, Personnel.prenom).all()
            print(f"✅ Résultats de recherche sans limitation : {len(resultats_recherche)}")
            
            # Vérification que nous récupérons bien tout le personnel
            if len(personnel_defaut) == total_personnel:
                print("🎉 SUCCÈS : Affichage par défaut récupère tout le personnel")
            else:
                print(f"⚠️ ATTENTION : Affichage par défaut récupère {len(personnel_defaut)}/{total_personnel}")
            
            if len(resultats_recherche) == total_personnel:
                print("🎉 SUCCÈS : Recherche récupère tout le personnel")
            else:
                print(f"⚠️ ATTENTION : Recherche récupère {len(resultats_recherche)}/{total_personnel}")
            
            # Afficher quelques exemples
            print(f"\n📋 Premiers 5 résultats :")
            for i, p in enumerate(personnel_defaut[:5]):
                print(f"  {i+1}. {p.matricule} - {p.nom} {p.prenom}")
            
            if len(personnel_defaut) > 5:
                print(f"  ... et {len(personnel_defaut) - 5} autres")
            
            return True
            
        except Exception as e:
            print(f"❌ Erreur lors du test : {str(e)}")
            import traceback
            traceback.print_exc()
            return False

def test_recherche_specifique():
    """Test de recherche spécifique"""
    
    with app.app_context():
        try:
            # Test de recherche par nom
            search_term = "OUALI"
            resultats = Personnel.query.filter(
                Personnel.nom.ilike(f'%{search_term}%')
            ).all()
            
            print(f"\n🔍 Recherche '{search_term}' : {len(resultats)} résultat(s)")
            
            for p in resultats:
                print(f"  - {p.matricule}: {p.nom} {p.prenom}")
            
            return True
            
        except Exception as e:
            print(f"❌ Erreur lors de la recherche : {str(e)}")
            return False

if __name__ == "__main__":
    print("🧪 Test d'affichage complet du personnel RH")
    print("=" * 50)
    
    success1 = test_affichage_complet()
    success2 = test_recherche_specifique()
    
    print("\n" + "=" * 50)
    if success1 and success2:
        print("🎉 TOUS LES TESTS RÉUSSIS")
        print("✅ L'affichage complet fonctionne correctement")
    else:
        print("❌ CERTAINS TESTS ONT ÉCHOUÉ")
