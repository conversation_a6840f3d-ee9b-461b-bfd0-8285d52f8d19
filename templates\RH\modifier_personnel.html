{% extends "rh/base_rh.html" %}

{% block title %}Modifier Personnel - {{ militaire.nom_complet }}{% endblock %}

{% block content %}
<div class="fade-in">
    <!-- En-tête -->
    <div class="row mb-4">
        <div class="col-12">
            <div class="card-military">
                <div class="card-header-military">
                    <div class="row align-items-center">
                        <div class="col-md-8">
                            <h2 class="mb-0">
                                <i class="fas fa-edit"></i>
                                Modifier Informations Personnel
                            </h2>
                            <small style="color: var(--text-light);">{{ militaire.nom_complet }} - {{ militaire.matricule }}</small>
                        </div>
                        <div class="col-md-4 text-end">
                            <a href="{{ url_for('rh.fiche_personnel', matricule=militaire.matricule) }}" class="btn btn-info">
                                <i class="fas fa-arrow-left"></i> Retour à la Fiche
                            </a>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Formulaire de Modification -->
    <form method="POST" action="{{ url_for('rh.modifier_informations_personnelles', matricule=militaire.matricule) }}">
        <div class="row">
            <!-- Section État Matrimonial -->
            <div class="col-md-6 mb-4">
                <div class="card-military">
                    <div class="card-header-military">
                        <h5 class="mb-0"><i class="fas fa-heart me-2"></i>État Matrimonial</h5>
                    </div>
                    <div class="card-body">
                        <select class="form-select" name="situation_familiale_id" required>
                            <option value="">-- Sélectionner --</option>
                            {% for etat in etats_matrimoniaux %}
                            <option value="{{ etat.id_sitfam }}" {% if militaire.situation_familiale_id == etat.id_sitfam %}selected{% endif %}>
                                {{ etat.libelle }}
                            </option>
                            {% endfor %}
                        </select>
                    </div>
                </div>
            </div>

            <!-- Section Grade -->
            <div class="col-md-6 mb-4">
                <div class="card-military">
                    <div class="card-header-military">
                        <h5 class="mb-0"><i class="fas fa-star me-2"></i>Grade</h5>
                    </div>
                    <div class="card-body">
                        <div class="mb-3">
                            <label class="form-label fw-bold">Nouveau Grade</label>
                            <select class="form-select" name="grade_id" id="gradeSelect" required>
                                <option value="">-- Sélectionner --</option>
                                {% for grade in grades %}
                                <option value="{{ grade.id_grade }}" 
                                    data-categorie="{{ grade.categorie_id }}"
                                    {% if militaire.grade_actuel_id == grade.id_grade %}selected{% endif %}
                                    {% if loop.index0 > 0 and grades[loop.index0-1].id_grade != militaire.grade_actuel_id %}disabled{% endif %}>
                                    {{ grade.libelle }}
                                </option>
                                {% endfor %}
                            </select>
                            <small class="text-muted">Seul le grade suivant est disponible</small>
                        </div>
                        <div class="mb-3">
                            <label class="form-label fw-bold">Date de Nomination</label>
                            <input type="date" class="form-control" name="date_nomination" required>
                        </div>
                        <div class="mb-3">
                            <label class="form-label fw-bold">Catégorie (automatique)</label>
                            <input type="text" class="form-control" id="categorieAuto" readonly>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <div class="row">
            <!-- Section Unité -->
            <div class="col-md-6 mb-4">
                <div class="card-military">
                    <div class="card-header-military">
                        <h5 class="mb-0"><i class="fas fa-building me-2"></i>Unité</h5>
                    </div>
                    <div class="card-body">
                        <div class="mb-3">
                            <label class="form-label fw-bold">Nouvelle Unité</label>
                            <select class="form-select" name="unite_id" required>
                                <option value="">-- Sélectionner --</option>
                                {% for unite in unites %}
                                <option value="{{ unite.id_unite }}" {% if militaire.unite_id == unite.id_unite %}selected{% endif %}>
                                    {{ unite.libelle }}
                                </option>
                                {% endfor %}
                            </select>
                        </div>
                        <div class="mb-3">
                            <label class="form-label fw-bold">Date d'Affectation</label>
                            <input type="date" class="form-control" name="date_affectation" required>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Section Fonction -->
            <div class="col-md-6 mb-4">
                <div class="card-military">
                    <div class="card-header-military">
                        <h5 class="mb-0"><i class="fas fa-briefcase me-2"></i>Fonction</h5>
                    </div>
                    <div class="card-body">
                        <div class="mb-3">
                            <label class="form-label fw-bold">Nouvelle Fonction</label>
                            <input type="text" class="form-control" name="fonction" value="" placeholder="Saisir la fonction" required>
                        </div>
                        <div class="mb-3">
                            <label class="form-label fw-bold">Date Prise de Fonction</label>
                            <input type="date" class="form-control" name="date_prise_fonction" required>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <div class="row">
            <!-- Section Contact -->
            <div class="col-md-6 mb-4">
                <div class="card-military">
                    <div class="card-header-military">
                        <h5 class="mb-0"><i class="fas fa-phone me-2"></i>Contact</h5>
                    </div>
                    <div class="card-body">
                        <label class="form-label fw-bold">GSM</label>
                        <input type="tel" class="form-control" name="gsm" value="{{ militaire.gsm or '' }}" placeholder="Numéro de téléphone">
                    </div>
                </div>
            </div>

            <!-- Section Adresse -->
            <div class="col-md-6 mb-4">
                <div class="card-military">
                    <div class="card-header-military">
                        <h5 class="mb-0"><i class="fas fa-home me-2"></i>Adresse</h5>
                    </div>
                    <div class="card-body">
                        <textarea class="form-control" name="adresse" rows="3" placeholder="Adresse de résidence">{{ militaire.lieu_residence or '' }}</textarea>
                    </div>
                </div>
            </div>
        </div>

        <!-- Boutons d'Action -->
        <div class="row">
            <div class="col-12">
                <div class="card-military">
                    <div class="card-body text-center">
                        <button type="submit" class="btn btn-success-military btn-lg me-3">
                            <i class="fas fa-save"></i> Enregistrer les Modifications
                        </button>
                        <a href="{{ url_for('rh.fiche_personnel', matricule=militaire.matricule) }}" class="btn btn-secondary btn-lg">
                            <i class="fas fa-times"></i> Annuler
                        </a>
                    </div>
                </div>
            </div>
        </div>
    </form>
</div>
{% endblock %}

{% block extra_js %}
<script>
// Gestion de la logique des grades et catégories
document.addEventListener('DOMContentLoaded', function() {
    const gradeSelect = document.getElementById('gradeSelect');
    const categorieAuto = document.getElementById('categorieAuto');

    // Règles de liaison Grade → Catégorie
    const gradeCategories = {
        // Officier
        'sous-lieutenant': 'Officier',
        'lieutenant': 'Officier',
        'capitaine': 'Officier',
        'commandant': 'Officier',
        'lt-colonel': 'Officier',
        'colonel': 'Officier',

        // Officier du rang
        'MDL': 'Officier du rang',
        'MDL Chef': 'Officier du rang',
        'adjudant': 'Officier du rang',
        'adjudant chef': 'Officier du rang',

        // Militaire du rang
        'soldat 1°classe': 'Militaire du rang',
        'soldat 2° classe': 'Militaire du rang',
        'brigadier': 'Militaire du rang',
        'brigadier chef': 'Militaire du rang'
    };

    // Grade actuel du militaire
    const gradeActuelId = {{ militaire.grade_actuel_id or 'null' }};

    // Fonction pour gérer la logique des grades
    function gererLogiquesGrades() {
        const options = gradeSelect.querySelectorAll('option');
        let gradeActuelTrouve = false;
        let gradeSuivantTrouve = false;

        options.forEach((option, index) => {
            if (option.value === '') return; // Ignorer l'option vide

            const gradeId = parseInt(option.value);

            if (gradeId === gradeActuelId) {
                gradeActuelTrouve = true;
                option.disabled = false; // Grade actuel disponible
            } else if (gradeActuelTrouve && !gradeSuivantTrouve) {
                // Premier grade après le grade actuel
                option.disabled = false;
                gradeSuivantTrouve = true;
            } else {
                // Tous les autres grades sont grisés
                option.disabled = true;
            }
        });
    }

    // Appliquer la logique au chargement
    gererLogiquesGrades();

    // Gestion du changement automatique de catégorie
    if (gradeSelect) {
        gradeSelect.addEventListener('change', function() {
            const selectedOption = this.options[this.selectedIndex];
            const gradeLibelle = selectedOption.textContent.trim();

            // Trouver la catégorie correspondante
            if (gradeCategories[gradeLibelle]) {
                categorieAuto.value = gradeCategories[gradeLibelle];
            } else {
                categorieAuto.value = '';
            }
        });

        // Déclencher l'événement au chargement pour afficher la catégorie actuelle
        gradeSelect.dispatchEvent(new Event('change'));
    }
});
</script>
{% endblock %}
