#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Script de debug pour le formulaire
"""

import sys
import os
from flask import Flask, render_template_string
from db import db
from rh_models import *

def create_app():
    """Créer l'application Flask pour le contexte de base de données"""
    app = Flask(__name__)
    app.config['SQLALCHEMY_DATABASE_URI'] = 'mysql+pymysql://root:@localhost/gestion_vehicules'
    app.config['SQLALCHEMY_TRACK_MODIFICATIONS'] = False
    db.init_app(app)
    return app

def debug_formulaire():
    """Debug du formulaire"""
    app = create_app()
    
    with app.app_context():
        try:
            print("🔍 Debug du formulaire d'ajout de militaire...")
            
            # Charger les données comme dans la route
            genres = ReferentielGenre.query.all()
            categories = ReferentielCategorie.query.all()
            groupes_sanguins = ReferentielGroupeSanguin.query.all()
            services = ReferentielArme.query.all()  # Services/Armes
            specialites = ReferentielSpecialite.query.all()
            unites = ReferentielUnite.query.all()
            grades = ReferentielGrade.query.all()
            etats_matrimoniaux = ReferentielSituationFamiliale.query.all()
            liens_parente = ReferentielDegreParente.query.all()
            
            print(f"\n📊 Données chargées:")
            print(f"- Genres: {len(genres)}")
            print(f"- Catégories: {len(categories)}")
            print(f"- Groupes sanguins: {len(groupes_sanguins)}")
            print(f"- Services: {len(services)}")
            print(f"- Spécialités: {len(specialites)}")
            print(f"- Unités: {len(unites)}")
            print(f"- Grades: {len(grades)}")
            print(f"- États matrimoniaux: {len(etats_matrimoniaux)}")
            print(f"- Liens parenté: {len(liens_parente)}")
            
            # Test du template pour les états matrimoniaux
            template_etat = """
            {% for etat in etats_matrimoniaux %}
            <option value="{{ etat.id_sitfam }}">{{ etat.libelle }}</option>
            {% endfor %}
            """
            
            print(f"\n🔍 Test template États matrimoniaux:")
            result = render_template_string(template_etat, etats_matrimoniaux=etats_matrimoniaux)
            print(result)
            
            # Test du template pour les services
            template_service = """
            {% for service in services %}
            <option value="{{ service.id_arme }}">{{ service.libelle }}</option>
            {% endfor %}
            """
            
            print(f"\n🔍 Test template Services:")
            result = render_template_string(template_service, services=services)
            print(result)
            
            # Test du template pour les liens de parenté
            template_lien = """
            {% for lien in liens_parente %}
            <option value="{{ lien.id_degre }}">{{ lien.libelle }}</option>
            {% endfor %}
            """
            
            print(f"\n🔍 Test template Liens de parenté:")
            result = render_template_string(template_lien, liens_parente=liens_parente)
            print(result)
            
            print("\n✅ Debug terminé!")
            return True
            
        except Exception as e:
            print(f"❌ Erreur lors du debug: {str(e)}")
            import traceback
            traceback.print_exc()
            return False

if __name__ == "__main__":
    success = debug_formulaire()
    sys.exit(0 if success else 1)
