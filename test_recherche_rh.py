#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Test de la recherche RH via l'interface web
"""

from flask import Flask
from db import db, init_app
from rh_models import *
from rh_blueprint import rh_bp

def create_test_app():
    """Créer l'application Flask pour les tests"""
    app = Flask(__name__)
    app.secret_key = 'test_key'
    init_app(app)
    app.register_blueprint(rh_bp)
    return app

def test_recherche_web():
    """Test de la recherche via l'interface web"""
    app = create_test_app()
    
    with app.test_client() as client:
        with app.app_context():
            print("=" * 60)
            print("TEST DE LA RECHERCHE VIA L'INTERFACE WEB")
            print("=" * 60)
            
            # Test 1: Page de recherche par défaut
            print("\n1. Test de la page de recherche par défaut:")
            response = client.get('/rh/recherche')
            print(f"   Status: {response.status_code}")
            if response.status_code == 200:
                print("   ✅ Page de recherche accessible")
            else:
                print(f"   ❌ Erreur: {response.status_code}")
                print(f"   Données: {response.data.decode()[:200]}...")
            
            # Test 2: Recherche par nom
            print("\n2. Test recherche par nom 'OUALI':")
            response = client.get('/rh/recherche?search=OUALI')
            print(f"   Status: {response.status_code}")
            if response.status_code == 200:
                content = response.data.decode()
                if 'OUALI' in content:
                    print("   ✅ Résultats trouvés pour OUALI")
                else:
                    print("   ⚠️  Aucun résultat affiché pour OUALI")
            else:
                print(f"   ❌ Erreur: {response.status_code}")
            
            # Test 3: Recherche par matricule
            print("\n3. Test recherche par matricule '1056663':")
            response = client.get('/rh/recherche?matricule=1056663')
            print(f"   Status: {response.status_code}")
            if response.status_code == 200:
                content = response.data.decode()
                if '1056663' in content:
                    print("   ✅ Résultats trouvés pour matricule 1056663")
                else:
                    print("   ⚠️  Aucun résultat affiché pour matricule 1056663")
            else:
                print(f"   ❌ Erreur: {response.status_code}")
            
            # Test 4: Recherche via POST
            print("\n4. Test recherche via POST:")
            response = client.post('/rh/recherche', data={'search': 'BENALI'})
            print(f"   Status: {response.status_code}")
            if response.status_code == 200:
                content = response.data.decode()
                if 'BENALI' in content:
                    print("   ✅ Résultats trouvés pour BENALI via POST")
                else:
                    print("   ⚠️  Aucun résultat affiché pour BENALI via POST")
            else:
                print(f"   ❌ Erreur: {response.status_code}")

def test_donnees_base():
    """Vérifier les données dans la base"""
    app = create_test_app()
    
    with app.app_context():
        print("\n" + "=" * 60)
        print("VÉRIFICATION DES DONNÉES DE BASE")
        print("=" * 60)
        
        # Quelques exemples de personnel
        personnel_exemples = Personnel.query.limit(5).all()
        print(f"\nPersonnel trouvé: {len(personnel_exemples)}")
        
        for p in personnel_exemples:
            print(f"  - {p.matricule}: {p.nom} {p.prenom}")
            print(f"    Arme: {p.arme.libelle if p.arme else 'N/A'}")
            print(f"    Unité: {p.unite.libelle if p.unite else 'N/A'}")
            print(f"    Grade: {p.grade_actuel.libelle if p.grade_actuel else 'N/A'}")
            print()

if __name__ == "__main__":
    test_donnees_base()
    test_recherche_web()
