#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Test pour vérifier que l'erreur TypeError est corrigée
"""

import sys
import os
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from app import app
from rh_models import Personnel
from db import db

def test_pagination_object():
    """Test que l'objet de pagination est correct"""
    
    with app.app_context():
        try:
            print("🧪 Test de l'objet de pagination")
            print("=" * 60)
            
            # Test de la requête de pagination
            page = 1
            per_page = 100
            
            personnel_paginated = Personnel.query.join(Personnel.arme)\
                                                .join(Personnel.unite)\
                                                .join(Personnel.grade_actuel)\
                                                .join(Personnel.categorie)\
                                                .order_by(Personnel.nom, Personnel.prenom)\
                                                .paginate(page=page, per_page=per_page, error_out=False)
            
            print(f"✅ Objet de pagination créé: {type(personnel_paginated)}")
            print(f"✅ Attribut items: {type(personnel_paginated.items)}")
            print(f"✅ Nombre d'items: {len(personnel_paginated.items)}")
            print(f"✅ Total: {personnel_paginated.total}")
            print(f"✅ Pages: {personnel_paginated.pages}")
            
            # Test d'itération sur les items
            print(f"\n🔄 Test d'itération sur personnel.items:")
            for i, militaire in enumerate(personnel_paginated.items[:3]):
                print(f"   {i+1}. {militaire.nom} {militaire.prenom}")
            
            # Test des attributs de pagination
            print(f"\n📄 Attributs de pagination:")
            print(f"   - has_prev: {personnel_paginated.has_prev}")
            print(f"   - has_next: {personnel_paginated.has_next}")
            print(f"   - page: {personnel_paginated.page}")
            print(f"   - per_page: {personnel_paginated.per_page}")
            
            # Test de iter_pages()
            print(f"\n🔢 Test iter_pages():")
            pages = list(personnel_paginated.iter_pages())
            print(f"   Pages disponibles: {pages}")
            
            return True
            
        except Exception as e:
            print(f"❌ Erreur lors du test : {str(e)}")
            import traceback
            traceback.print_exc()
            return False

def test_empty_pagination():
    """Test de l'objet EmptyPagination"""
    
    try:
        print("\n🧪 Test de l'objet EmptyPagination")
        print("=" * 60)
        
        # Créer l'objet EmptyPagination comme dans rh_blueprint.py
        class EmptyPagination:
            def __init__(self):
                self.items = []
                self.total = 0
                self.pages = 0
                self.page = 1
                self.per_page = 100
                self.has_prev = False
                self.has_next = False
                self.prev_num = None
                self.next_num = None
            
            def iter_pages(self):
                return []
        
        empty_pagination = EmptyPagination()
        
        print(f"✅ Objet EmptyPagination créé: {type(empty_pagination)}")
        print(f"✅ Attribut items: {type(empty_pagination.items)}")
        print(f"✅ Nombre d'items: {len(empty_pagination.items)}")
        print(f"✅ Total: {empty_pagination.total}")
        print(f"✅ Pages: {empty_pagination.pages}")
        
        # Test d'itération (doit être vide)
        print(f"\n🔄 Test d'itération sur empty_pagination.items:")
        count = 0
        for militaire in empty_pagination.items:
            count += 1
        print(f"   Nombre d'éléments itérés: {count}")
        
        # Test des attributs
        print(f"\n📄 Attributs de pagination vide:")
        print(f"   - has_prev: {empty_pagination.has_prev}")
        print(f"   - has_next: {empty_pagination.has_next}")
        print(f"   - page: {empty_pagination.page}")
        print(f"   - per_page: {empty_pagination.per_page}")
        
        # Test de iter_pages()
        print(f"\n🔢 Test iter_pages() vide:")
        pages = list(empty_pagination.iter_pages())
        print(f"   Pages disponibles: {pages}")
        
        return True
        
    except Exception as e:
        print(f"❌ Erreur lors du test EmptyPagination : {str(e)}")
        import traceback
        traceback.print_exc()
        return False

def test_date_context():
    """Test que l'objet date est disponible"""
    
    try:
        print("\n🧪 Test de l'objet date")
        print("=" * 60)
        
        from datetime import date
        
        print(f"✅ Import date réussi: {type(date)}")
        print(f"✅ date.today(): {date.today()}")
        print(f"✅ Type de date.today(): {type(date.today())}")
        
        # Test de calcul de différence (comme dans le template)
        from datetime import date, timedelta
        
        date_future = date.today() + timedelta(days=30)
        date_passee = date.today() - timedelta(days=10)
        
        jours_restants_futur = (date_future - date.today()).days
        jours_restants_passe = (date_passee - date.today()).days
        
        print(f"\n📅 Test de calculs de dates:")
        print(f"   Date future (+30j): {date_future}")
        print(f"   Jours restants: {jours_restants_futur}")
        print(f"   Date passée (-10j): {date_passee}")
        print(f"   Jours restants: {jours_restants_passe}")
        
        return True
        
    except Exception as e:
        print(f"❌ Erreur lors du test date : {str(e)}")
        return False

if __name__ == "__main__":
    print("🧪 Test de correction de l'erreur TypeError")
    print("=" * 60)
    
    success1 = test_pagination_object()
    success2 = test_empty_pagination()
    success3 = test_date_context()
    
    print("\n" + "=" * 60)
    if success1 and success2 and success3:
        print("🎉 TOUS LES TESTS RÉUSSIS")
        print("✅ L'erreur TypeError est corrigée")
        print("📋 Corrections validées:")
        print("   - Objet de pagination correct")
        print("   - EmptyPagination fonctionnel")
        print("   - Contexte date disponible")
        print("   - Itération sur .items possible")
    else:
        print("❌ CERTAINS TESTS ONT ÉCHOUÉ")
        print("⚠️ L'erreur TypeError peut persister")
